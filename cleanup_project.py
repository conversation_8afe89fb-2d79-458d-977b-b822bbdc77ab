#!/usr/bin/env python3
"""
项目清理脚本
删除旧的项目结构和临时文件，保留新的项目结构
"""

import shutil
import os
from pathlib import Path

def cleanup_old_project():
    """清理旧的项目目录"""
    old_project = Path("report_auto_RA")
    
    if old_project.exists():
        print(f"🗑️  删除旧项目目录: {old_project}")
        shutil.rmtree(old_project)
        print("✅ 旧项目目录已删除")
    else:
        print("⚠️  旧项目目录不存在")

def cleanup_temp_files():
    """清理临时文件和脚本"""
    temp_files = [
        "restructure_project.py",
        "migrate_code.py", 
        "update_imports.py",
        "cleanup_project.py"  # 最后删除自己
    ]
    
    for file_name in temp_files[:-1]:  # 先删除其他文件
        file_path = Path(file_name)
        if file_path.exists():
            print(f"🗑️  删除临时文件: {file_name}")
            file_path.unlink()
            print(f"✅ {file_name} 已删除")

def cleanup_temp_directories():
    """清理临时目录"""
    temp_dirs = [
        "tests",  # 根目录下的tests目录（不是新项目中的）
    ]
    
    for dir_name in temp_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            print(f"🗑️  删除临时目录: {dir_name}")
            shutil.rmtree(dir_path)
            print(f"✅ {dir_name} 目录已删除")

def cleanup_documentation():
    """清理多余的文档文件"""
    doc_files = [
        "PROJECT_STRUCTURE.md",  # 保留 FINAL_PROJECT_STRUCTURE.md
        "TEST_SUMMARY.md"        # 可以保留作为历史记录
    ]
    
    for file_name in doc_files:
        file_path = Path(file_name)
        if file_path.exists():
            print(f"🗑️  删除文档文件: {file_name}")
            file_path.unlink()
            print(f"✅ {file_name} 已删除")

def rename_new_project():
    """将新项目目录重命名为正式名称"""
    old_name = Path("report_auto_RA_new")
    new_name = Path("report_auto_RA")
    
    if old_name.exists():
        if new_name.exists():
            print("❌ 目标目录已存在，无法重命名")
            return False
        
        print(f"📁 重命名项目目录: {old_name} -> {new_name}")
        old_name.rename(new_name)
        print("✅ 项目目录重命名完成")
        return True
    else:
        print("⚠️  新项目目录不存在")
        return False

def cleanup_pycache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    for pycache_dir in Path(".").rglob("__pycache__"):
        if pycache_dir.is_dir():
            print(f"🗑️  删除缓存目录: {pycache_dir}")
            shutil.rmtree(pycache_dir)
    
    # 查找所有.pyc文件
    for pyc_file in Path(".").rglob("*.pyc"):
        print(f"🗑️  删除缓存文件: {pyc_file}")
        pyc_file.unlink()
    
    print("✅ Python缓存文件清理完成")

def create_final_structure_summary():
    """创建最终的项目结构总结"""
    summary_content = """# Report Auto RA - 项目清理完成

## 🎉 清理结果

### ✅ 已删除的内容
- 旧项目目录 `report_auto_RA/`
- 临时脚本文件
- 多余的文档文件
- Python缓存文件
- 临时测试目录

### 📁 保留的内容
- 新项目结构 `report_auto_RA/` (重命名后)
- 最终项目结构文档 `FINAL_PROJECT_STRUCTURE.md`

## 📋 下一步

现在可以开始基于新的项目结构进行开发：

1. **进入项目目录**
   ```bash
   cd report_auto_RA
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

4. **运行测试**
   ```bash
   python tests/test_runner.py
   ```

## 🎯 项目已准备就绪！

新的模块化项目结构已经完全就绪，可以开始后续的开发工作。
"""
    
    with open("PROJECT_CLEANUP_SUMMARY.md", "w", encoding="utf-8") as f:
        f.write(summary_content)
    
    print("📄 创建项目清理总结文档")

def main():
    """主函数"""
    print("🧹 开始项目清理...")
    print("=" * 60)
    
    # 确认操作
    response = input("⚠️  这将删除旧项目目录和临时文件，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    print("\n🚀 开始清理...")
    
    # 清理Python缓存
    cleanup_pycache()
    print()
    
    # 清理旧项目
    cleanup_old_project()
    print()
    
    # 清理临时目录
    cleanup_temp_directories()
    print()
    
    # 清理文档文件
    cleanup_documentation()
    print()
    
    # 重命名新项目
    if rename_new_project():
        print()
    
    # 清理临时文件
    cleanup_temp_files()
    print()
    
    # 创建总结文档
    create_final_structure_summary()
    
    print("\n" + "=" * 60)
    print("🎉 项目清理完成！")
    print("\n📁 当前项目结构:")
    print("├── report_auto_RA/          # 主项目目录")
    print("├── FINAL_PROJECT_STRUCTURE.md")
    print("└── PROJECT_CLEANUP_SUMMARY.md")
    print("\n🎯 现在可以开始基于新结构进行开发！")
    print("=" * 60)
    
    # 最后删除自己
    print("\n🗑️  删除清理脚本...")
    try:
        Path(__file__).unlink()
        print("✅ 清理脚本已删除")
    except:
        print("⚠️  请手动删除 cleanup_project.py")

if __name__ == "__main__":
    main()
