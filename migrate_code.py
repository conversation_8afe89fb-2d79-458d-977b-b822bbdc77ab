#!/usr/bin/env python3
"""
代码迁移脚本
将现有代码迁移到新的项目结构
"""

import shutil
from pathlib import Path

def migrate_database_modules():
    """迁移数据库相关模块"""
    old_base = Path("report_auto_RA/src")
    new_base = Path("report_auto_RA_new")
    
    # 迁移数据库配置
    if (old_base / "database/config.py").exists():
        shutil.copy2(
            old_base / "database/config.py",
            new_base / "core/database/models.py"
        )
        print("✅ 迁移数据库配置 -> core/database/models.py")
    
    # 迁移数据库管理器
    if (old_base / "database/manager.py").exists():
        shutil.copy2(
            old_base / "database/manager.py", 
            new_base / "core/database/manager.py"
        )
        print("✅ 迁移数据库管理器 -> core/database/manager.py")
    
    # 迁移数据库适配器
    adapters_old = old_base / "database/adapters"
    adapters_new = new_base / "core/database/adapters"
    
    if adapters_old.exists():
        for file in adapters_old.glob("*.py"):
            if file.name == "__init__.py":
                continue
            elif file.name == "base_adapter.py":
                shutil.copy2(file, adapters_new / "base.py")
                print(f"✅ 迁移 {file.name} -> adapters/base.py")
            elif file.name == "mysql_adapter.py":
                shutil.copy2(file, adapters_new / "mysql.py")
                print(f"✅ 迁移 {file.name} -> adapters/mysql.py")
            elif file.name == "oracle_adapter.py":
                shutil.copy2(file, adapters_new / "oracle.py")
                print(f"✅ 迁移 {file.name} -> adapters/oracle.py")

def migrate_ui_modules():
    """迁移UI模块"""
    old_base = Path("report_auto_RA/src/ui")
    new_base = Path("report_auto_RA_new/ui")
    
    if not old_base.exists():
        print("⚠️ 原UI目录不存在")
        return
    
    # 迁移主窗口
    if (old_base / "main_window.py").exists():
        shutil.copy2(old_base / "main_window.py", new_base / "main_window.py")
        print("✅ 迁移主窗口 -> ui/main_window.py")
    
    # 迁移对话框
    dialog_files = [
        ("database_config_dialog.py", "database_config.py"),
        ("excel_file_manager.py", "excel_manager.py"),
        ("sql_script_manager.py", "script_manager.py"),
        ("settings_dialog.py", "settings.py"),
        ("about_dialog.py", "about.py")
    ]
    
    for old_name, new_name in dialog_files:
        old_file = old_base / old_name
        if old_file.exists():
            shutil.copy2(old_file, new_base / "dialogs" / new_name)
            print(f"✅ 迁移 {old_name} -> dialogs/{new_name}")

def migrate_utils_modules():
    """迁移工具模块"""
    old_base = Path("report_auto_RA/src/utils")
    new_base = Path("report_auto_RA_new/utils")
    
    if not old_base.exists():
        print("⚠️ 原utils目录不存在")
        return
    
    # 迁移工具文件
    util_files = [
        "logger.py",
        "encryption.py", 
        "file_utils.py",
        "date_utils.py",
        "directory.py"
    ]
    
    for file_name in util_files:
        old_file = old_base / file_name
        if old_file.exists():
            shutil.copy2(old_file, new_base / file_name)
            print(f"✅ 迁移 {file_name} -> utils/{file_name}")

def migrate_exceptions():
    """迁移异常模块"""
    old_base = Path("report_auto_RA/src/exceptions")
    new_base = Path("report_auto_RA_new/exceptions")
    
    if not old_base.exists():
        print("⚠️ 原exceptions目录不存在")
        return
    
    if (old_base / "app_exceptions.py").exists():
        shutil.copy2(old_base / "app_exceptions.py", new_base / "base.py")
        print("✅ 迁移异常定义 -> exceptions/base.py")

def migrate_config_modules():
    """迁移配置模块"""
    old_base = Path("report_auto_RA/src/config")
    new_base = Path("report_auto_RA_new/config")
    
    if not old_base.exists():
        print("⚠️ 原config目录不存在")
        return
    
    if (old_base / "config_manager.py").exists():
        shutil.copy2(old_base / "config_manager.py", new_base / "manager.py")
        print("✅ 迁移配置管理器 -> config/manager.py")

def migrate_tests():
    """迁移测试文件"""
    old_base = Path("report_auto_RA/tests")
    new_base = Path("report_auto_RA_new/tests")
    
    if not old_base.exists():
        print("⚠️ 原tests目录不存在")
        return
    
    # 迁移测试文件到unit目录
    test_files = [
        ("test_database_config.py", "unit/core/test_database_models.py"),
        ("test_database_manager.py", "unit/core/test_database_manager.py"),
        ("test_encryption.py", "unit/utils/test_encryption.py")
    ]
    
    for old_name, new_path in test_files:
        old_file = old_base / old_name
        if old_file.exists():
            new_file = new_base / new_path
            new_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(old_file, new_file)
            print(f"✅ 迁移 {old_name} -> tests/{new_path}")
    
    # 迁移测试运行脚本
    if (old_base / "run_tests.py").exists():
        shutil.copy2(old_base / "run_tests.py", new_base / "test_runner.py")
        print("✅ 迁移测试运行器 -> tests/test_runner.py")

def migrate_other_files():
    """迁移其他文件"""
    old_base = Path("report_auto_RA")
    new_base = Path("report_auto_RA_new")
    
    # 迁移requirements.txt
    if (old_base / "requirements.txt").exists():
        shutil.copy2(old_base / "requirements.txt", new_base / "requirements.txt")
        print("✅ 迁移 requirements.txt")
    
    # 迁移README.md
    if (old_base / "README.md").exists():
        shutil.copy2(old_base / "README.md", new_base / "README.md")
        print("✅ 迁移 README.md")
    
    # 迁移配置文件
    config_old = old_base / "config"
    config_new = new_base / "data/config"
    
    if config_old.exists():
        for file in config_old.glob("*.json"):
            shutil.copy2(file, config_new / file.name)
            print(f"✅ 迁移配置文件 {file.name} -> data/config/")

def main():
    """主函数"""
    print("🚀 开始代码迁移...")
    print("="*60)
    
    # 检查源目录是否存在
    if not Path("report_auto_RA").exists():
        print("❌ 源项目目录不存在")
        return
    
    if not Path("report_auto_RA_new").exists():
        print("❌ 新项目目录不存在，请先运行 restructure_project.py")
        return
    
    # 执行迁移
    migrate_database_modules()
    print()
    
    migrate_ui_modules()
    print()
    
    migrate_utils_modules()
    print()
    
    migrate_exceptions()
    print()
    
    migrate_config_modules()
    print()
    
    migrate_tests()
    print()
    
    migrate_other_files()
    
    print("\n" + "="*60)
    print("🎉 代码迁移完成！")
    print("\n📋 下一步:")
    print("1. 更新导入路径")
    print("2. 创建缺失的模块")
    print("3. 运行测试验证")
    print("="*60)

if __name__ == "__main__":
    main()
