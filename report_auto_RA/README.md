# Excel数据库查询填充工具 (Report Auto RA)

## 项目简介
一个基于Python tkinter的桌面应用程序，支持通过配置连接Oracle/MySQL数据库，读取Excel模板并根据SQL查询结果填充数据。

## 功能特性
- 🔗 支持Oracle和MySQL数据库连接
- 📊 Excel模板数据自动填充
- ⚙️ 可视化配置管理界面
- 📝 SQL脚本管理和版本控制
- 💾 基于文件的数据存储（JSON/CSV）
- 🖥️ 简洁友好的用户界面

## 技术栈
- Python 3.8+
- tkinter (GUI框架)
- openpyxl (Excel处理)
- cx_Oracle (Oracle数据库)
- PyMySQL (MySQL数据库)
- JSON (配置存储)

## 项目结构
```
report_auto_RA/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── config/                # 配置文件目录
│   ├── database.json      # 数据库配置
│   ├── sheet_sql_mapping.json # 映射关系
│   └── app_settings.json  # 应用设置
├── scripts/               # SQL脚本存储
│   └── sql_scripts.json
├── templates/             # Excel模板目录
├── output/                # 输出文件目录
├── logs/                  # 日志文件
├── backup/                # 备份文件
├── history/               # 历史记录
├── assets/                # 资源文件
│   └── icons/             # 图标文件
├── docs/                  # 文档目录
│   ├── user_manual.md     # 用户手册
│   └── api_docs.md        # API文档
├── tests/                 # 测试文件
└── src/                   # 源代码目录
    ├── __init__.py
    ├── database/          # 数据库模块
    ├── excel/             # Excel处理模块
    ├── config/            # 配置管理模块
    ├── scripts/           # SQL脚本管理模块
    ├── ui/                # 用户界面模块
    ├── storage/           # 数据存储模块
    ├── utils/             # 工具模块
    └── exceptions/        # 异常处理模块
```

## 安装和使用
1. 克隆项目到本地
2. 安装依赖：`pip install -r requirements.txt`
3. 运行程序：`python main.py`

## 开发状态
🚧 项目正在开发中...

## 版本历史
- v1.0.0 - 初始版本，基础框架搭建