"""
应用程序主类
"""

import logging
from pathlib import Path
from config.manager import ConfigManager
from utils.logger import setup_logger

class ReportAutoApp:
    """Report Auto RA 应用程序主类"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = setup_logger()
        self.logger.info("应用程序初始化")
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动 Report Auto RA")
            
            # 初始化UI
            from ui.main_window import MainWindow
            
            # 创建并显示主窗口
            main_window = MainWindow()
            return main_window.run()
            
        except Exception as e:
            self.logger.error(f"应用程序启动失败: {e}")
            return 1
