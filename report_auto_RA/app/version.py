"""
版本信息
"""

__version__ = "1.0.0"
__author__ = "Report Auto RA Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

VERSION_INFO = {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "pre_release": None
}

def get_version():
    """获取版本字符串"""
    version = f"{VERSION_INFO['major']}.{VERSION_INFO['minor']}.{VERSION_INFO['patch']}"
    if VERSION_INFO['pre_release']:
        version += f"-{VERSION_INFO['pre_release']}"
    return version
