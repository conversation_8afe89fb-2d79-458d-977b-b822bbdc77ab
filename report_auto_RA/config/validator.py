"""
配置验证器
"""

import json
from pathlib import Path
from typing import Dict, Any, List, Tuple
from ..exceptions.config import ConfigValidationError

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.schemas_dir = Path(__file__).parent / "schemas"
    
    def validate_database_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证数据库配置
        
        Args:
            config: 数据库配置字典
            
        Returns:
            tuple: (是否有效, 错误消息列表)
        """
        errors = []
        
        # 必填字段检查
        required_fields = ['name', 'db_type', 'host', 'username', 'database']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"缺少必填字段: {field}")
        
        # 数据库类型检查
        if config.get('db_type') not in ['mysql', 'oracle']:
            errors.append("数据库类型必须是mysql或oracle")
        
        # 端口号检查
        port = config.get('port', 0)
        if not isinstance(port, int) or not (1 <= port <= 65535):
            errors.append("端口号必须在1-65535之间")
        
        return len(errors) == 0, errors
    
    def validate_app_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证应用配置
        
        Args:
            config: 应用配置字典
            
        Returns:
            tuple: (是否有效, 错误消息列表)
        """
        errors = []
        
        # 检查日志级别
        log_level = config.get('logging', {}).get('level', 'INFO')
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if log_level not in valid_levels:
            errors.append(f"无效的日志级别: {log_level}")
        
        # 检查UI主题
        theme = config.get('ui', {}).get('theme', 'default')
        valid_themes = ['default', 'dark', 'light']
        if theme not in valid_themes:
            errors.append(f"无效的UI主题: {theme}")
        
        return len(errors) == 0, errors
    
    def validate_with_schema(self, config: Dict[str, Any], schema_name: str) -> Tuple[bool, List[str]]:
        """
        使用JSON Schema验证配置
        
        Args:
            config: 配置字典
            schema_name: 模式文件名
            
        Returns:
            tuple: (是否有效, 错误消息列表)
        """
        try:
            import jsonschema
            
            schema_file = self.schemas_dir / f"{schema_name}.json"
            if not schema_file.exists():
                return False, [f"模式文件不存在: {schema_name}.json"]
            
            with open(schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            jsonschema.validate(config, schema)
            return True, []
            
        except ImportError:
            # 如果没有安装jsonschema，使用内置验证
            if schema_name == 'database':
                return self.validate_database_config(config)
            elif schema_name == 'application':
                return self.validate_app_config(config)
            else:
                return True, []  # 默认通过
                
        except jsonschema.ValidationError as e:
            return False, [str(e)]
        except Exception as e:
            return False, [f"验证过程中出错: {str(e)}"]
