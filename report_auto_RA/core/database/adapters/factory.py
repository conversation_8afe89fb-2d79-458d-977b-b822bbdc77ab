"""
数据库适配器工厂
"""

from typing import Type, Dict
from .base import BaseAdapter
from .mysql import MySQLAdapter
from .oracle import OracleAdapter
from ...exceptions.database import UnsupportedDatabaseError

class AdapterFactory:
    """数据库适配器工厂类"""
    
    _adapters: Dict[str, Type[BaseAdapter]] = {
        'mysql': MySQLAdapter,
        'oracle': OracleAdapter
    }
    
    @classmethod
    def create_adapter(cls, db_type: str, config) -> BaseAdapter:
        """
        创建数据库适配器
        
        Args:
            db_type: 数据库类型
            config: 数据库配置
            
        Returns:
            BaseAdapter: 数据库适配器实例
            
        Raises:
            UnsupportedDatabaseError: 不支持的数据库类型
        """
        adapter_class = cls._adapters.get(db_type.lower())
        if not adapter_class:
            raise UnsupportedDatabaseError(f"不支持的数据库类型: {db_type}")
        
        return adapter_class(config)
    
    @classmethod
    def register_adapter(cls, db_type: str, adapter_class: Type[BaseAdapter]):
        """
        注册新的数据库适配器
        
        Args:
            db_type: 数据库类型
            adapter_class: 适配器类
        """
        cls._adapters[db_type.lower()] = adapter_class
    
    @classmethod
    def get_supported_types(cls) -> list:
        """获取支持的数据库类型列表"""
        return list(cls._adapters.keys())
    
    @classmethod
    def is_supported(cls, db_type: str) -> bool:
        """检查是否支持指定的数据库类型"""
        return db_type.lower() in cls._adapters
