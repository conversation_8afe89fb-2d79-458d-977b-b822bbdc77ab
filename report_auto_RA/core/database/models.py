"""
数据库配置类
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置类"""
    
    name: str = ""
    db_type: str = "mysql"  # 'mysql' 或 'oracle'
    host: str = "localhost"
    port: int = 3306
    username: str = ""
    password: str = ""
    database: str = ""  # MySQL数据库名或Oracle SID
    description: str = ""
    created_time: str = ""
    updated_time: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        if self.db_type == "oracle" and self.port == 3306:
            self.port = 1521
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DatabaseConfig':
        """从字典创建配置"""
        return cls(**{k: v for k, v in data.items() if k in cls.__dataclass_fields__})
    
    def validate(self) -> Tu<PERSON>[bool, str]:
        """验证配置"""
        if not self.name.strip():
            return False, "配置名称不能为空"
        if not self.host.strip():
            return False, "主机地址不能为空"
        if not self.username.strip():
            return False, "用户名不能为空"
        if not self.database.strip():
            return False, "数据库名不能为空"
        if self.db_type not in ['mysql', 'oracle']:
            return False, "数据库类型必须是mysql或oracle"
        if not (1 <= self.port <= 65535):
            return False, "端口号必须在1-65535之间"
        
        return True, ""
    
    def get_connection_string(self) -> str:
        """获取连接字符串（用于显示，不包含密码）"""
        return f"{self.db_type}://{self.username}@{self.host}:{self.port}/{self.database}"
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        return f"{self.name} ({self.db_type.upper()})"