"""
应用程序异常定义
"""


class ReportAutoException(Exception):
    """Report Auto RA 应用程序基础异常"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

# 保持向后兼容
AppException = ReportAutoException


class DatabaseException(AppException):
    """数据库相关异常"""
    pass


class ExcelException(AppException):
    """Excel处理相关异常"""
    pass


class ConfigException(AppException):
    """配置相关异常"""
    pass


class SQLException(AppException):
    """SQL脚本相关异常"""
    pass


class UIException(AppException):
    """用户界面相关异常"""
    pass


class StorageException(AppException):
    """存储相关异常"""
    pass


class ValidationException(AppException):
    """数据验证异常"""
    pass


class FileOperationException(AppException):
    """文件操作异常"""
    pass