"""
Excel相关异常
"""

from .base import ReportAutoException

class ExcelException(ReportAutoException):
    """Excel异常基类"""
    pass

class FileNotFoundError(ExcelException):
    """Excel文件未找到异常"""
    pass

class FileFormatError(ExcelException):
    """Excel文件格式异常"""
    pass

class SheetNotFoundError(ExcelException):
    """工作表未找到异常"""
    pass

class CellError(ExcelException):
    """单元格操作异常"""
    pass

class TemplateError(ExcelException):
    """模板异常"""
    pass
