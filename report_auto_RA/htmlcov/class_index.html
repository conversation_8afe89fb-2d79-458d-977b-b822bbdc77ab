<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">62%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-29 09:27 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html">src\database\__init__.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce___init___py.html">src\database\adapters\__init__.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t10">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t10"><data value='BaseAdapter'>BaseAdapter</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t15">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t15"><data value='MySQLAdapter'>MySQLAdapter</data></a></td>
                <td>38</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="12 38">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t15">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t15"><data value='OracleAdapter'>OracleAdapter</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t13">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t13"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>19</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="11 19">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t15">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t15"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>57</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="28 57">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8___init___py.html">src\exceptions\__init__.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t6">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t6"><data value='AppException'>AppException</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t14">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t14"><data value='DatabaseException'>DatabaseException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t19">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t19"><data value='ExcelException'>ExcelException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t24">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t24"><data value='ConfigException'>ConfigException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t29">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t29"><data value='SQLException'>SQLException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t34">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t34"><data value='UIException'>UIException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t39">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t39"><data value='StorageException'>StorageException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t44">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t44"><data value='ValidationException'>ValidationException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t49">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t49"><data value='FileOperationException'>FileOperationException</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t9">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t9"><data value='DateUtils'>DateUtils</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="11 26">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t13">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t13"><data value='PasswordEncryption'>PasswordEncryption</data></a></td>
                <td>20</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="12 20">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t17">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t17"><data value='FileUtils'>FileUtils</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html">src\utils\logger.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="7 23">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t15">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t15"><data value='TestDatabaseConfig'>TestDatabaseConfig</data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t17">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t17"><data value='TestDatabaseManager'>TestDatabaseManager</data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t15">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t15"><data value='TestPasswordEncryption'>TestPasswordEncryption</data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>568</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="354 568">62%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-29 09:27 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
