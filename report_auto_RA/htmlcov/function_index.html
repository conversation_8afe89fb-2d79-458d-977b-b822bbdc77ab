<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">62%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html">src\database\__init__.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce___init___py.html">src\database\adapters\__init__.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t13">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t13"><data value='init__'>BaseAdapter.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t18">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t18"><data value='connect'>BaseAdapter.connect</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t23">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t23"><data value='test_connection'>BaseAdapter.test_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t28">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t28"><data value='execute_query'>BaseAdapter.execute_query</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t33">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t33"><data value='close'>BaseAdapter.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t37">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html#t37"><data value='is_connected'>BaseAdapter.is_connected</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html">src\database\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t18">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t18"><data value='connect'>MySQLAdapter.connect</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t41">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t41"><data value='test_connection'>MySQLAdapter.test_connection</data></a></td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="6 12">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t57">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t57"><data value='execute_query'>MySQLAdapter.execute_query</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t77">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html#t77"><data value='close'>MySQLAdapter.close</data></a></td>
                <td>7</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="1 7">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html">src\database\adapters\mysql_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t18">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t18"><data value='connect'>OracleAdapter.connect</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t42">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t42"><data value='test_connection'>OracleAdapter.test_connection</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t58">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t58"><data value='execute_query'>OracleAdapter.execute_query</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t87">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html#t87"><data value='close'>OracleAdapter.close</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html">src\database\adapters\oracle_adapter.py</a></td>
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t27">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t27"><data value='post_init__'>DatabaseConfig.__post_init__</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t32">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t32"><data value='to_dict'>DatabaseConfig.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t37">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t37"><data value='from_dict'>DatabaseConfig.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t41">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t41"><data value='validate'>DatabaseConfig.validate</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t58">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t58"><data value='get_connection_string'>DatabaseConfig.get_connection_string</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t62">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t62"><data value='get_display_name'>DatabaseConfig.get_display_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t18">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t18"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t26">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t26"><data value='set_config'>DatabaseManager.set_config</data></a></td>
                <td>15</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="9 15">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t52">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t52"><data value='test_connection'>DatabaseManager.test_connection</data></a></td>
                <td>20</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="16 20">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t82">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t82"><data value='execute_query'>DatabaseManager.execute_query</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t99">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t99"><data value='get_current_config'>DatabaseManager.get_current_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t103">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t103"><data value='is_connected'>DatabaseManager.is_connected</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t107">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html#t107"><data value='close'>DatabaseManager.close</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html">src\database\manager.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8___init___py.html">src\exceptions\__init__.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t8">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html#t8"><data value='init__'>AppException.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html">src\exceptions\app_exceptions.py</a></td>
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t13">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t13"><data value='get_current_timestamp'>DateUtils.get_current_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t18">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t18"><data value='get_current_date'>DateUtils.get_current_date</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t23">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t23"><data value='get_current_datetime'>DateUtils.get_current_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t28">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t28"><data value='format_datetime'>DateUtils.format_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t33">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t33"><data value='parse_datetime'>DateUtils.parse_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t38">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html#t38"><data value='get_output_filename'>DateUtils.get_output_filename</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html">src\utils\date_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t12">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t12"><data value='ensure_directories'>ensure_directories</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t39">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t39"><data value='get_project_root'>get_project_root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t44">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t44"><data value='get_config_dir'>get_config_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t49">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t49"><data value='get_output_dir'>get_output_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t54">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t54"><data value='get_templates_dir'>get_templates_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t59">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t59"><data value='get_logs_dir'>get_logs_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t64">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html#t64"><data value='get_backup_dir'>get_backup_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html">src\utils\directory.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t16">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t16"><data value='init__'>PasswordEncryption.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t20">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t20"><data value='get_or_create_key'>PasswordEncryption._get_or_create_key</data></a></td>
                <td>12</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="4 12">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t46">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t46"><data value='encrypt'>PasswordEncryption.encrypt</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t52">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t52"><data value='decrypt'>PasswordEncryption.decrypt</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t63">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t63"><data value='encrypt_password'>encrypt_password</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t68">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html#t68"><data value='decrypt_password'>decrypt_password</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html">src\utils\encryption.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t21">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t21"><data value='read_json'>FileUtils.read_json</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t34">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t34"><data value='write_json'>FileUtils.write_json</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t48">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t48"><data value='backup_file'>FileUtils.backup_file</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t73">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t73"><data value='cleanup_old_files'>FileUtils.cleanup_old_files</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t91">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html#t91"><data value='get_file_size'>FileUtils.get_file_size</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html">src\utils\file_utils.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html#t13">src\utils\logger.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html#t13"><data value='setup_logging'>setup_logging</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html">src\utils\logger.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t18">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t18"><data value='test_valid_mysql_config'>TestDatabaseConfig.test_valid_mysql_config</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t34">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t34"><data value='test_valid_oracle_config'>TestDatabaseConfig.test_valid_oracle_config</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t50">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t50"><data value='test_invalid_config_missing_fields'>TestDatabaseConfig.test_invalid_config_missing_fields</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t66">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t66"><data value='test_invalid_db_type'>TestDatabaseConfig.test_invalid_db_type</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t82">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html#t82"><data value='test_connection_string_generation'>TestDatabaseConfig.test_connection_string_generation</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html">tests\test_database_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t20">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t20"><data value='setup_method'>TestDatabaseManager.setup_method</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t33">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t33"><data value='test_manager_initialization'>TestDatabaseManager.test_manager_initialization</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t40">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t40"><data value='test_set_current_config'>TestDatabaseManager.test_set_current_config</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t45">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t45"><data value='test_connection_test_success'>TestDatabaseManager.test_connection_test_success</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t63">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t63"><data value='test_connection_test_failure'>TestDatabaseManager.test_connection_test_failure</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t76">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t76"><data value='test_connection_test_no_config'>TestDatabaseManager.test_connection_test_no_config</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t83">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html#t83"><data value='test_unsupported_database_type'>TestDatabaseManager.test_unsupported_database_type</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html">tests\test_database_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t18">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t18"><data value='test_encrypt_decrypt_password'>TestPasswordEncryption.test_encrypt_decrypt_password</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t31">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t31"><data value='test_empty_password'>TestPasswordEncryption.test_empty_password</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t36">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t36"><data value='test_none_password'>TestPasswordEncryption.test_none_password</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t46">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t46"><data value='test_encryption_consistency'>TestPasswordEncryption.test_encryption_consistency</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t58">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html#t58"><data value='test_encryption_instance'>TestPasswordEncryption.test_encryption_instance</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html">tests\test_encryption.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>568</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="354 568">62%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
