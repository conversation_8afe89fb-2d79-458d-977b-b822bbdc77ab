<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">62%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html">src\database\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce___init___py.html">src\database\adapters\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_base_adapter_py.html">src\database\adapters\base_adapter.py</a></td>
                <td>21</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="16 21">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_mysql_adapter_py.html">src\database\adapters\mysql_adapter.py</a></td>
                <td>50</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="24 50">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c820082ab42387ce_oracle_adapter_py.html">src\database\adapters\oracle_adapter.py</a></td>
                <td>57</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="12 57">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html">src\database\config.py</a></td>
                <td>42</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="34 42">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_manager_py.html">src\database\manager.py</a></td>
                <td>72</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="43 72">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8___init___py.html">src\exceptions\__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c8b1c7de43f31f8_app_exceptions_py.html">src\exceptions\app_exceptions.py</a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_date_utils_py.html">src\utils\date_utils.py</a></td>
                <td>22</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="15 22">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_directory_py.html">src\utils\directory.py</a></td>
                <td>26</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="11 26">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_encryption_py.html">src\utils\encryption.py</a></td>
                <td>36</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="28 36">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_utils_py.html">src\utils\file_utils.py</a></td>
                <td>73</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="20 73">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logger_py.html">src\utils\logger.py</a></td>
                <td>23</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="7 23">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_config_py.html">tests\test_database_config.py</a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_database_manager_py.html">tests\test_database_manager.py</a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_encryption_py.html">tests\test_encryption.py</a></td>
                <td>34</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="32 34">94%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>568</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="354 568">62%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_a44f0ac069e85531_test_encryption_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
