{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "ce928c3a8dd7a8b79ec0db4f49727fd4", "files": {"z_145eef247bfb46b6___init___py": {"hash": "924502ce42399f3f518b92bd87fde836", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d___init___py": {"hash": "9dc0db45404a7201fd05db1c48e1dcc6", "index": {"url": "z_67d056e3707ef46d___init___py.html", "file": "src\\database\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c820082ab42387ce___init___py": {"hash": "2101896d0799973cc74867f5b822b9e8", "index": {"url": "z_c820082ab42387ce___init___py.html", "file": "src\\database\\adapters\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c820082ab42387ce_base_adapter_py": {"hash": "eab6a635a4cad0e8fc18e9c8d7ebb3ae", "index": {"url": "z_c820082ab42387ce_base_adapter_py.html", "file": "src\\database\\adapters\\base_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c820082ab42387ce_mysql_adapter_py": {"hash": "729a477a94c271caa4bd8d965f75e218", "index": {"url": "z_c820082ab42387ce_mysql_adapter_py.html", "file": "src\\database\\adapters\\mysql_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c820082ab42387ce_oracle_adapter_py": {"hash": "f5ef8eb37ad73fa5d953cbe5caee6ef2", "index": {"url": "z_c820082ab42387ce_oracle_adapter_py.html", "file": "src\\database\\adapters\\oracle_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_config_py": {"hash": "95631acb8dd8d6767949091aa89626cf", "index": {"url": "z_67d056e3707ef46d_config_py.html", "file": "src\\database\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_manager_py": {"hash": "0dd4632b161e7e3dba758ff0edeb7aca", "index": {"url": "z_67d056e3707ef46d_manager_py.html", "file": "src\\database\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c8b1c7de43f31f8___init___py": {"hash": "86060cd4d132268811b6435911279d9f", "index": {"url": "z_5c8b1c7de43f31f8___init___py.html", "file": "src\\exceptions\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c8b1c7de43f31f8_app_exceptions_py": {"hash": "9ee5e4006b9d9a410ac35e1c284b84c3", "index": {"url": "z_5c8b1c7de43f31f8_app_exceptions_py.html", "file": "src\\exceptions\\app_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be___init___py": {"hash": "56baa255bf0e90e079aae10c0a8f32d9", "index": {"url": "z_6156a86a215061be___init___py.html", "file": "src\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_date_utils_py": {"hash": "267bb39e65a41f3b77cc7fad3341cc87", "index": {"url": "z_6156a86a215061be_date_utils_py.html", "file": "src\\utils\\date_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_directory_py": {"hash": "2b3b2dd99bebfe03c17ae102dfdfa17d", "index": {"url": "z_6156a86a215061be_directory_py.html", "file": "src\\utils\\directory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_encryption_py": {"hash": "8b35558942309d7123dfaf05ab051e52", "index": {"url": "z_6156a86a215061be_encryption_py.html", "file": "src\\utils\\encryption.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_file_utils_py": {"hash": "e9212db488ab6bc41fdc35b031c7e7f4", "index": {"url": "z_6156a86a215061be_file_utils_py.html", "file": "src\\utils\\file_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_logger_py": {"hash": "08a601af7b7bf8229ee0cf2358ffd3a8", "index": {"url": "z_6156a86a215061be_logger_py.html", "file": "src\\utils\\logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_database_config_py": {"hash": "53f5f0200a1ce7beb77046c6075042a2", "index": {"url": "z_a44f0ac069e85531_test_database_config_py.html", "file": "tests\\test_database_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_database_manager_py": {"hash": "501af3ef5e5edbebe17465bbad3c3dcd", "index": {"url": "z_a44f0ac069e85531_test_database_manager_py.html", "file": "tests\\test_database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_encryption_py": {"hash": "12ec21f44ff30bc320e93a186501ea5a", "index": {"url": "z_a44f0ac069e85531_test_encryption_py.html", "file": "tests\\test_encryption.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}