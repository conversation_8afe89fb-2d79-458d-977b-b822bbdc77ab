<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for tests\test_database_config.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>tests\test_database_config.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">31 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">31<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_6156a86a215061be_logger_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_a44f0ac069e85531_test_database_manager_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">&#25968;&#25454;&#24211;&#37197;&#32622;&#27979;&#35797;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">pytest</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">pathlib</span> <span class="key">import</span> <span class="nam">Path</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="com"># &#28155;&#21152;src&#30446;&#24405;&#21040;&#36335;&#24452;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="nam">sys</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">insert</span><span class="op">(</span><span class="num">0</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">Path</span><span class="op">(</span><span class="nam">__file__</span><span class="op">)</span><span class="op">.</span><span class="nam">parent</span><span class="op">.</span><span class="nam">parent</span> <span class="op">/</span> <span class="str">'src'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">database</span><span class="op">.</span><span class="nam">config</span> <span class="key">import</span> <span class="nam">DatabaseConfig</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">class</span> <span class="nam">TestDatabaseConfig</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="str">"""&#25968;&#25454;&#24211;&#37197;&#32622;&#27979;&#35797;&#31867;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="key">def</span> <span class="nam">test_valid_mysql_config</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">        <span class="str">"""&#27979;&#35797;&#26377;&#25928;&#30340;MySQL&#37197;&#32622;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">DatabaseConfig</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">"test_mysql"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">            <span class="nam">db_type</span><span class="op">=</span><span class="str">"mysql"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="str">"localhost"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="num">3306</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">            <span class="nam">username</span><span class="op">=</span><span class="str">"test_user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">            <span class="nam">password</span><span class="op">=</span><span class="str">"test_pass"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">            <span class="nam">database</span><span class="op">=</span><span class="str">"test_db"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="nam">is_valid</span><span class="op">,</span> <span class="nam">error_msg</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">validate</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">        <span class="key">assert</span> <span class="nam">is_valid</span> <span class="key">is</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="key">assert</span> <span class="nam">error_msg</span> <span class="op">==</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="key">def</span> <span class="nam">test_valid_oracle_config</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="str">"""&#27979;&#35797;&#26377;&#25928;&#30340;Oracle&#37197;&#32622;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">DatabaseConfig</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">"test_oracle"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">            <span class="nam">db_type</span><span class="op">=</span><span class="str">"oracle"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="str">"localhost"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="num">1521</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">            <span class="nam">username</span><span class="op">=</span><span class="str">"test_user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">            <span class="nam">password</span><span class="op">=</span><span class="str">"test_pass"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">            <span class="nam">database</span><span class="op">=</span><span class="str">"XE"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="nam">is_valid</span><span class="op">,</span> <span class="nam">error_msg</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">validate</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="key">assert</span> <span class="nam">is_valid</span> <span class="key">is</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="key">assert</span> <span class="nam">error_msg</span> <span class="op">==</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="key">def</span> <span class="nam">test_invalid_config_missing_fields</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="str">"""&#27979;&#35797;&#32570;&#23569;&#24517;&#22635;&#23383;&#27573;&#30340;&#37197;&#32622;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">DatabaseConfig</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">            <span class="nam">db_type</span><span class="op">=</span><span class="str">"mysql"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="num">3306</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">            <span class="nam">username</span><span class="op">=</span><span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">            <span class="nam">password</span><span class="op">=</span><span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">            <span class="nam">database</span><span class="op">=</span><span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">        <span class="nam">is_valid</span><span class="op">,</span> <span class="nam">error_msg</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">validate</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="key">assert</span> <span class="nam">is_valid</span> <span class="key">is</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">        <span class="key">assert</span> <span class="str">"&#37197;&#32622;&#21517;&#31216;&#19981;&#33021;&#20026;&#31354;"</span> <span class="key">in</span> <span class="nam">error_msg</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="key">def</span> <span class="nam">test_invalid_db_type</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="str">"""&#27979;&#35797;&#19981;&#25903;&#25345;&#30340;&#25968;&#25454;&#24211;&#31867;&#22411;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">DatabaseConfig</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">"test"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">            <span class="nam">db_type</span><span class="op">=</span><span class="str">"postgresql"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="str">"localhost"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="num">5432</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">            <span class="nam">username</span><span class="op">=</span><span class="str">"test"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">            <span class="nam">password</span><span class="op">=</span><span class="str">"test"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="nam">database</span><span class="op">=</span><span class="str">"test"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="nam">is_valid</span><span class="op">,</span> <span class="nam">error_msg</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">validate</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="key">assert</span> <span class="nam">is_valid</span> <span class="key">is</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">        <span class="key">assert</span> <span class="str">"&#25968;&#25454;&#24211;&#31867;&#22411;&#24517;&#39035;&#26159;mysql&#25110;oracle"</span> <span class="key">in</span> <span class="nam">error_msg</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="key">def</span> <span class="nam">test_connection_string_generation</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="str">"""&#27979;&#35797;&#36830;&#25509;&#23383;&#31526;&#20018;&#29983;&#25104;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">DatabaseConfig</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">"test"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">            <span class="nam">db_type</span><span class="op">=</span><span class="str">"mysql"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="str">"localhost"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="num">3306</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="nam">username</span><span class="op">=</span><span class="str">"user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="nam">password</span><span class="op">=</span><span class="str">"pass"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="nam">database</span><span class="op">=</span><span class="str">"testdb"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="nam">conn_str</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">get_connection_string</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">        <span class="nam">expected</span> <span class="op">=</span> <span class="str">"mysql://user@localhost:3306/testdb"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="key">assert</span> <span class="nam">conn_str</span> <span class="op">==</span> <span class="nam">expected</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_6156a86a215061be_logger_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_a44f0ac069e85531_test_database_manager_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 18:29 +0800
        </p>
    </div>
</footer>
</body>
</html>
