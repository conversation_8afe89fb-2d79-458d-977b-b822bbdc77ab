#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据库查询填充工具 (Report Auto RA) - 主程序入口
Author: AI Assistant
Date: 2024-01-15
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / 'src'
sys.path.insert(0, str(src_dir))

from src.utils.logger import setup_logging
from src.utils.directory import ensure_directories
from src.ui.main_window import MainWindow
from src.exceptions.app_exceptions import AppException


def main():
    """主程序入口"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Report Auto RA 程序启动")
        
        # 确保必要的目录存在
        ensure_directories()
        
        # 创建并启动主窗口
        app = MainWindow()
        app.run()
        
    except AppException as e:
        logging.error(f"应用程序错误: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"未知错误: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logging.info("程序结束")


if __name__ == "__main__":
    main()