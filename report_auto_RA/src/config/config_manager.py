"""
配置管理器
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from ..database.config import DatabaseConfig
from ..utils.file_utils import FileUtils
from ..utils.directory import get_config_dir
from ..utils.encryption import encrypt_password, decrypt_password
from ..exceptions.app_exceptions import ConfigException

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = get_config_dir()
        self.db_config_file = self.config_dir / "database.json"
        self.mapping_config_file = self.config_dir / "sheet_sql_mapping.json"
        self.app_settings_file = self.config_dir / "app_settings.json"
    
    def load_database_configs(self) -> List[DatabaseConfig]:
        """加载数据库配置列表"""
        try:
            data = FileUtils.read_json(self.db_config_file)
            configs = []
            
            for config_data in data.get('configs', []):
                # 解密密码
                if config_data.get('password'):
                    try:
                        config_data['password'] = decrypt_password(config_data['password'])
                    except Exception:
                        logger.warning(f"解密密码失败，配置: {config_data.get('name', 'Unknown')}")
                        config_data['password'] = ""
                
                config = DatabaseConfig.from_dict(config_data)
                configs.append(config)
            
            logger.info(f"加载了 {len(configs)} 个数据库配置")
            return configs
            
        except Exception as e:
            logger.error(f"加载数据库配置失败: {e}")
            return []
    
    def save_database_configs(self, configs: List[DatabaseConfig]):
        """保存数据库配置列表"""
        try:
            # 加密密码
            config_data_list = []
            for config in configs:
                config_dict = config.to_dict()
                if config_dict.get('password'):
                    config_dict['password'] = encrypt_password(config_dict['password'])
                config_data_list.append(config_dict)
            
            data = {
                'configs': config_data_list,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            FileUtils.write_json(self.db_config_file, data)
            logger.info(f"保存了 {len(configs)} 个数据库配置")
            
        except Exception as e:
            logger.error(f"保存数据库配置失败: {e}")
            raise ConfigException(f"保存数据库配置失败: {e}")
    
    def get_current_database_config(self) -> Optional[str]:
        """获取当前数据库配置名称"""
        try:
            data = FileUtils.read_json(self.db_config_file)
            return data.get('current')
        except Exception:
            return None
    
    def set_current_database_config(self, config_name: str):
        """设置当前数据库配置"""
        try:
            data = FileUtils.read_json(self.db_config_file)
            data['current'] = config_name
            data['last_updated'] = datetime.now().isoformat()
            FileUtils.write_json(self.db_config_file, data)
            logger.info(f"设置当前数据库配置: {config_name}")
        except Exception as e:
            logger.error(f"设置当前数据库配置失败: {e}")
            raise ConfigException(f"设置当前数据库配置失败: {e}")
    
    def load_sheet_sql_mapping(self) -> Dict[str, str]:
        """加载Sheet-SQL映射关系"""
        try:
            data = FileUtils.read_json(self.mapping_config_file)
            mappings = data.get('mappings', {})
            logger.info(f"加载了 {len(mappings)} 个映射关系")
            return mappings
        except Exception as e:
            logger.error(f"加载映射关系失败: {e}")
            return {}
    
    def save_sheet_sql_mapping(self, mappings: Dict[str, str]):
        """保存Sheet-SQL映射关系"""
        try:
            data = {
                'mappings': mappings,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            FileUtils.write_json(self.mapping_config_file, data)
            logger.info(f"保存了 {len(mappings)} 个映射关系")
            
        except Exception as e:
            logger.error(f"保存映射关系失败: {e}")
            raise ConfigException(f"保存映射关系失败: {e}")
    
    def load_app_settings(self) -> Dict[str, Any]:
        """加载应用设置"""
        try:
            settings = FileUtils.read_json(self.app_settings_file)
            
            # 设置默认值
            default_settings = {
                'window_geometry': '1000x700',
                'theme': 'default',
                'auto_backup': True,
                'backup_days': 30,
                'log_level': 'INFO',
                'excel_start_row': 2,
                'date_format': '%Y-%m-%d',
                'output_directory': 'output'
            }
            
            # 合并默认设置
            for key, value in default_settings.items():
                if key not in settings:
                    settings[key] = value
            
            return settings
            
        except Exception as e:
            logger.error(f"加载应用设置失败: {e}")
            return {}
    
    def save_app_settings(self, settings: Dict[str, Any]):
        """保存应用设置"""
        try:
            settings['last_updated'] = datetime.now().isoformat()
            FileUtils.write_json(self.app_settings_file, settings)
            logger.info("应用设置已保存")
        except Exception as e:
            logger.error(f"保存应用设置失败: {e}")
            raise ConfigException(f"保存应用设置失败: {e}")
    
    def backup_configs(self) -> str:
        """备份所有配置文件"""
        try:
            backup_files = []
            
            for config_file in [self.db_config_file, self.mapping_config_file, self.app_settings_file]:
                if config_file.exists():
                    backup_path = FileUtils.backup_file(config_file)
                    backup_files.append(str(backup_path))
            
            logger.info(f"配置文件备份完成，共备份 {len(backup_files)} 个文件")
            return f"备份完成，共 {len(backup_files)} 个文件"
            
        except Exception as e:
            logger.error(f"备份配置文件失败: {e}")
            raise ConfigException(f"备份配置文件失败: {e}")