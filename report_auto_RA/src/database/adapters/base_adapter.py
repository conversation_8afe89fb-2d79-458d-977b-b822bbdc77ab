"""
数据库适配器基类
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ..config import DatabaseConfig


class BaseAdapter(ABC):
    """数据库适配器基类"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection = None
    
    @abstractmethod
    def connect(self):
        """建立连接"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接"""
        pass
    
    @abstractmethod
    def execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行查询"""
        pass
    
    @abstractmethod
    def close(self):
        """关闭连接"""
        pass
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connection is not None