"""
Oracle数据库适配器
"""

import cx_Oracle
import logging
from typing import List, Dict, Any
from .base_adapter import BaseAdapter
from ...exceptions.app_exceptions import DatabaseException
from ...utils.encryption import decrypt_password

logger = logging.getLogger(__name__)


class OracleAdapter(BaseAdapter):
    """Oracle数据库适配器"""
    
    def connect(self):
        """建立Oracle连接"""
        try:
            password = decrypt_password(self.config.password) if self.config.password else ""
            
            # 创建DSN
            dsn = cx_Oracle.makedsn(
                self.config.host,
                self.config.port,
                service_name=self.config.database
            )
            
            self.connection = cx_Oracle.connect(
                user=self.config.username,
                password=password,
                dsn=dsn,
                encoding="UTF-8"
            )
            logger.info(f"Oracle连接建立成功: {self.config.get_connection_string()}")
            
        except Exception as e:
            logger.error(f"Oracle连接失败: {e}")
            raise DatabaseException(f"Oracle连接失败: {e}")
    
    def test_connection(self) -> bool:
        """测试Oracle连接"""
        try:
            self.connect()
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1 FROM DUAL")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"Oracle连接测试失败: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()
                self.connection = None
    
    def execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行Oracle查询"""
        if not self.connection:
            self.connect()
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                
                # 获取数据并转换为字典列表
                results = []
                for row in cursor.fetchall():
                    row_dict = {}
                    for i, value in enumerate(row):
                        # 处理Oracle特殊数据类型
                        if hasattr(value, 'read'):  # CLOB/BLOB
                            value = value.read()
                        row_dict[columns[i]] = value
                    results.append(row_dict)
                
                return results
                
        except Exception as e:
            logger.error(f"Oracle查询执行失败: {e}")
            raise DatabaseException(f"查询执行失败: {e}")
    
    def close(self):
        """关闭Oracle连接"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("Oracle连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Oracle连接时出现警告: {e}")
            finally:
                self.connection = None