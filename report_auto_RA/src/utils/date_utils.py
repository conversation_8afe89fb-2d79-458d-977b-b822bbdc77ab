"""
日期时间工具
"""

from datetime import datetime, date
from typing import str


class DateUtils:
    """日期时间工具类"""
    
    @staticmethod
    def get_current_timestamp() -> str:
        """获取当前时间戳字符串"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    @staticmethod
    def get_current_date() -> str:
        """获取当前日期字符串"""
        return date.today().strftime("%Y%m%d")
    
    @staticmethod
    def get_current_datetime() -> str:
        """获取当前日期时间字符串"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化日期时间"""
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """解析日期时间字符串"""
        return datetime.strptime(date_str, format_str)
    
    @staticmethod
    def get_output_filename(prefix: str = "report", extension: str = "xlsx") -> str:
        """生成输出文件名"""
        timestamp = DateUtils.get_current_timestamp()
        return f"{prefix}_{timestamp}.{extension}"