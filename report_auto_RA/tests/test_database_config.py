"""
数据库配置测试
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from src.database.config import DatabaseConfig


class TestDatabaseConfig:
    """数据库配置测试类"""
    
    def test_valid_mysql_config(self):
        """测试有效的MySQL配置"""
        config = DatabaseConfig(
            name="test_mysql",
            db_type="mysql",
            host="localhost",
            port=3306,
            username="test_user",
            password="test_pass",
            database="test_db"
        )
        
        is_valid, error_msg = config.validate()
        assert is_valid is True
        assert error_msg == ""
    
    def test_valid_oracle_config(self):
        """测试有效的Oracle配置"""
        config = DatabaseConfig(
            name="test_oracle",
            db_type="oracle",
            host="localhost",
            port=1521,
            username="test_user",
            password="test_pass",
            database="XE"
        )
        
        is_valid, error_msg = config.validate()
        assert is_valid is True
        assert error_msg == ""
    
    def test_invalid_config_missing_fields(self):
        """测试缺少必填字段的配置"""
        config = DatabaseConfig(
            name="",
            db_type="mysql",
            host="",
            port=3306,
            username="",
            password="",
            database=""
        )
        
        is_valid, error_msg = config.validate()
        assert is_valid is False
        assert "配置名称不能为空" in error_msg
    
    def test_invalid_db_type(self):
        """测试不支持的数据库类型"""
        config = DatabaseConfig(
            name="test",
            db_type="postgresql",
            host="localhost",
            port=5432,
            username="test",
            password="test",
            database="test"
        )
        
        is_valid, error_msg = config.validate()
        assert is_valid is False
        assert "不支持的数据库类型" in error_msg
    
    def test_connection_string_generation(self):
        """测试连接字符串生成"""
        config = DatabaseConfig(
            name="test",
            db_type="mysql",
            host="localhost",
            port=3306,
            username="user",
            password="pass",
            database="testdb"
        )
        
        conn_str = config.get_connection_string()
        expected = "mysql://user:***@localhost:3306/testdb"
        assert conn_str == expected