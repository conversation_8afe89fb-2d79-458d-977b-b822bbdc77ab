"""
数据库管理器测试
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from src.database.manager import DatabaseManager
from src.database.config import DatabaseConfig


class TestDatabaseManager:
    """数据库管理器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.manager = DatabaseManager()
        self.test_config = DatabaseConfig(
            name="test_config",
            db_type="mysql",
            host="localhost",
            port=3306,
            username="test_user",
            password="test_pass",
            database="test_db"
        )
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        assert self.manager is not None
        assert hasattr(self.manager, '_adapters')
        assert 'mysql' in self.manager._adapters
        assert 'oracle' in self.manager._adapters
    
    def test_set_current_config(self):
        """测试设置当前配置"""
        self.manager.set_config(self.test_config)
        assert self.manager.current_config == self.test_config
    
    def test_connection_test_success(self):
        """测试连接测试成功"""
        # 模拟适配器
        mock_adapter = Mock()
        mock_adapter.test_connection.return_value = True

        # 直接替换manager中的适配器
        self.manager._adapters['mysql'] = Mock(return_value=mock_adapter)

        # 测试连接
        success, message = self.manager.test_connection(self.test_config)

        assert success is True
        assert "连接成功" in message
        mock_adapter.test_connection.assert_called_once()
        mock_adapter.close.assert_called_once()
    
    @patch('src.database.adapters.mysql_adapter.MySQLAdapter')
    def test_connection_test_failure(self, mock_adapter_class):
        """测试连接测试失败"""
        # 模拟适配器连接失败
        mock_adapter = Mock()
        mock_adapter.test_connection.return_value = False
        mock_adapter_class.return_value = mock_adapter
        
        # 测试连接
        success, message = self.manager.test_connection(self.test_config)
        
        assert success is False
        assert "连接失败" in message
    
    def test_connection_test_no_config(self):
        """测试无配置时的连接测试"""
        success, message = self.manager.test_connection()
        
        assert success is False
        assert "没有可用的数据库配置" in message
    
    def test_unsupported_database_type(self):
        """测试不支持的数据库类型"""
        invalid_config = DatabaseConfig(
            name="invalid",
            db_type="postgresql",
            host="localhost",
            port=5432,
            username="test",
            password="test",
            database="test"
        )
        
        success, message = self.manager.test_connection(invalid_config)
        assert success is False
        assert "数据库类型必须是mysql或oracle" in message