"""
密码加密功能测试
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from src.utils.encryption import encrypt_password, decrypt_password, PasswordEncryption


class TestPasswordEncryption:
    """密码加密测试类"""
    
    def test_encrypt_decrypt_password(self):
        """测试密码加密解密"""
        original_password = "test_password_123"
        
        # 加密
        encrypted = encrypt_password(original_password)
        assert encrypted != original_password
        assert len(encrypted) > 0
        
        # 解密
        decrypted = decrypt_password(encrypted)
        assert decrypted == original_password
    
    def test_empty_password(self):
        """测试空密码处理"""
        assert encrypt_password("") == ""
        assert decrypt_password("") == ""
    
    def test_none_password(self):
        """测试None密码处理"""
        # 这里需要处理None值的情况
        try:
            result = encrypt_password(None)
            assert result == ""
        except (TypeError, AttributeError):
            # 如果抛出异常，说明需要在加密函数中处理None值
            pass
    
    def test_encryption_consistency(self):
        """测试加密一致性"""
        password = "consistent_test"
        
        # 多次加密同一密码应该产生不同结果（因为有随机因子）
        encrypted1 = encrypt_password(password)
        encrypted2 = encrypt_password(password)
        
        # 但解密结果应该相同
        assert decrypt_password(encrypted1) == password
        assert decrypt_password(encrypted2) == password
    
    def test_encryption_instance(self):
        """测试加密实例创建"""
        encryption = PasswordEncryption()
        
        password = "instance_test"
        encrypted = encryption.encrypt(password)
        decrypted = encryption.decrypt(encrypted)
        
        assert decrypted == password