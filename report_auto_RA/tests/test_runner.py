"""
测试运行脚本
"""

import sys
import pytest
from pathlib import Path

def run_database_tests():
    """运行数据库相关测试"""
    test_dir = Path(__file__).parent
    project_root = test_dir.parent

    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))
    
    # 运行特定测试文件
    test_files = [
        "test_database_config.py",
        "test_encryption.py", 
        "test_database_manager.py"
    ]
    
    print("🧪 开始运行数据库连接管理器集成测试...")
    print("=" * 50)
    
    for test_file in test_files:
        test_path = test_dir / test_file
        if test_path.exists():
            print(f"\n📋 运行测试: {test_file}")
            result = pytest.main(["-v", str(test_path)])
            if result != 0:
                print(f"❌ 测试失败: {test_file}")
                return False
            else:
                print(f"✅ 测试通过: {test_file}")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")
    
    print("\n" + "=" * 50)
    print("🎉 所有数据库测试完成!")
    return True

if __name__ == "__main__":
    success = run_database_tests()
    sys.exit(0 if success else 1)