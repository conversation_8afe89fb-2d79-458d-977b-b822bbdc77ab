#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from datetime import datetime
from typing import Optional

from ..database.config import DatabaseConfig
from ..config.config_manager import ConfigManager
from ..exceptions.app_exceptions import DatabaseException

logger = logging.getLogger(__name__)


class DatabaseConfigDialog:
    """数据库配置对话框"""
    
    def __init__(self, parent, config_manager: ConfigManager):
        self.parent = parent
        self.config_manager = config_manager
        self.dialog = None
        self.result = False
        
        # 表单变量
        self.name_var = tk.StringVar()
        self.db_type_var = tk.StringVar(value="mysql")
        self.host_var = tk.StringVar(value="localhost")
        self.port_var = tk.IntVar(value=3306)
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.database_var = tk.StringVar()
        self.description_var = tk.StringVar()
        
        # 配置列表
        self.configs = []
        self.current_config: Optional[DatabaseConfig] = None
    
    def show(self) -> bool:
        """显示对话框"""
        self._create_dialog()
        self._load_configs()
        
        # 模态显示
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.wait_window()
        
        return self.result
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("数据库配置管理")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：配置列表
        left_frame = ttk.LabelFrame(paned, text="配置列表")
        paned.add(left_frame, weight=1)
        
        self._create_config_list(left_frame)
        
        # 右侧：配置编辑
        right_frame = ttk.LabelFrame(paned, text="配置详情")
        paned.add(right_frame, weight=2)
        
        self._create_config_form(right_frame)
        
        # 底部按钮
        self._create_buttons(main_frame)
    
    def _create_config_list(self, parent):
        """创建配置列表"""
        # 列表框架
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Treeview
        columns = ("名称", "类型", "主机")
        self.config_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.config_tree.heading(col, text=col)
            self.config_tree.column(col, width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.config_tree.yview)
        self.config_tree.configure(yscrollcommand=scrollbar.set)
        
        self.config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.config_tree.bind("<<TreeviewSelect>>", self._on_config_select)
        
        # 列表操作按钮
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="新建", command=self._new_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="复制", command=self._copy_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=self._delete_config).pack(side=tk.LEFT, padx=2)
    
    def _create_config_form(self, parent):
        """创建配置表单"""
        # 滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基本信息
        basic_frame = ttk.LabelFrame(scrollable_frame, text="基本信息")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 配置名称
        ttk.Label(basic_frame, text="配置名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        name_entry = ttk.Entry(basic_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        # 数据库类型
        ttk.Label(basic_frame, text="数据库类型:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        db_type_combo = ttk.Combobox(basic_frame, textvariable=self.db_type_var, 
                                    values=["mysql", "oracle"], state="readonly", width=28)
        db_type_combo.grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        db_type_combo.bind("<<ComboboxSelected>>", self._on_db_type_changed)
        
        # 描述
        ttk.Label(basic_frame, text="描述:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        desc_entry = ttk.Entry(basic_frame, textvariable=self.description_var, width=30)
        desc_entry.grid(row=2, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        basic_frame.columnconfigure(1, weight=1)
        
        # 连接信息
        conn_frame = ttk.LabelFrame(scrollable_frame, text="连接信息")
        conn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 主机地址
        ttk.Label(conn_frame, text="主机地址:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        host_entry = ttk.Entry(conn_frame, textvariable=self.host_var, width=30)
        host_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        # 端口
        ttk.Label(conn_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        port_entry = ttk.Entry(conn_frame, textvariable=self.port_var, width=30)
        port_entry.grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        # 用户名
        ttk.Label(conn_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        username_entry = ttk.Entry(conn_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=2, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        # 密码
        ttk.Label(conn_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        password_entry = ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=3, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        # 数据库名
        ttk.Label(conn_frame, text="数据库名:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        database_entry = ttk.Entry(conn_frame, textvariable=self.database_var, width=30)
        database_entry.grid(row=4, column=1, sticky=tk.W+tk.E, padx=5, pady=2)
        
        conn_frame.columnconfigure(1, weight=1)
        
        # 测试连接按钮
        test_frame = ttk.Frame(scrollable_frame)
        test_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(test_frame, text="测试连接", command=self._test_connection).pack(side=tk.LEFT)
        self.test_result_label = ttk.Label(test_frame, text="", foreground="blue")
        self.test_result_label.pack(side=tk.LEFT, padx=10)
        
        # 配置canvas和scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _create_buttons(self, parent):
        """创建底部按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="保存", command=self._save_config).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self._cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="应用", command=self._apply_config).pack(side=tk.RIGHT, padx=5)
    
    def _load_configs(self):
        """加载配置列表"""
        try:
            self.configs = self.config_manager.load_database_configs()
            self._refresh_config_list()
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def _refresh_config_list(self):
        """刷新配置列表"""
        # 清空现有项目
        for item in self.config_tree.get_children():
            self.config_tree.delete(item)
        
        # 添加配置项目
        for config in self.configs:
            self.config_tree.insert("", tk.END, values=(
                config.name,
                config.db_type.upper(),
                f"{config.host}:{config.port}"
            ))
    
    def _on_config_select(self, event):
        """配置选择事件"""
        selection = self.config_tree.selection()
        if not selection:
            return
        
        item = self.config_tree.item(selection[0])
        config_name = item['values'][0]
        
        # 查找配置
        for config in self.configs:
            if config.name == config_name:
                self.current_config = config
                self._load_config_to_form(config)
                break
    
    def _load_config_to_form(self, config: DatabaseConfig):
        """加载配置到表单"""
        self.name_var.set(config.name)
        self.db_type_var.set(config.db_type)
        self.host_var.set(config.host)
        self.port_var.set(config.port)
        self.username_var.set(config.username)
        self.password_var.set(config.password)
        self.database_var.set(config.database)
        self.description_var.set(config.description)
    
    def _on_db_type_changed(self, event):
        """数据库类型改变事件"""
        db_type = self.db_type_var.get()
        if db_type == "mysql":
            self.port_var.set(3306)
        elif db_type == "oracle":
            self.port_var.set(1521)
    
    def _new_config(self):
        """新建配置"""
        self.current_config = None
        self._clear_form()
    
    def _copy_config(self):
        """复制配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return
        
        # 复制当前配置并修改名称
        self.name_var.set(f"{self.current_config.name}_副本")
        self.current_config = None
    
    def _delete_config(self):
        """删除配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return
        
        if messagebox.askyesno("确认", f"确定要删除配置 '{self.current_config.name}' 吗？"):
            try:
                self.configs = [c for c in self.configs if c.name != self.current_config.name]
                self.config_manager.save_database_configs(self.configs)
                self._refresh_config_list()
                self._clear_form()
                self.current_config = None
                messagebox.showinfo("成功", "配置删除成功")
            except Exception as e:
                logger.error(f"删除配置失败: {e}")
                messagebox.showerror("错误", f"删除配置失败: {e}")
    
    def _clear_form(self):
        """清空表单"""
        self.name_var.set("")
        self.db_type_var.set("mysql")
        self.host_var.set("localhost")
        self.port_var.set(3306)
        self.username_var.set("")
        self.password_var.set("")
        self.database_var.set("")
        self.description_var.set("")
        self.test_result_label.config(text="")
    
    def _test_connection(self):
        """测试连接"""
        try:
            # 创建临时配置
            config = DatabaseConfig(
                name=self.name_var.get(),
                db_type=self.db_type_var.get(),
                host=self.host_var.get(),
                port=self.port_var.get(),
                username=self.username_var.get(),
                password=self.password_var.get(),
                database=self.database_var.get()
            )
            
            # 验证配置
            is_valid, error_msg = config.validate()
            if not is_valid:
                self.test_result_label.config(text=f"配置错误: {error_msg}", foreground="red")
                return
            
            # TODO: 实际测试连接
            self.test_result_label.config(text="连接测试成功", foreground="green")
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            self.test_result_label.config(text=f"连接失败: {e}", foreground="red")
    
    def _save_config(self):
        """保存配置"""
        try:
            # 创建配置对象
            config = DatabaseConfig(
                name=self.name_var.get(),
                db_type=self.db_type_var.get(),
                host=self.host_var.get(),
                port=self.port_var.get(),
                username=self.username_var.get(),
                password=self.password_var.get(),
                database=self.database_var.get(),
                description=self.description_var.get(),
                created_time=datetime.now().isoformat() if not self.current_config else self.current_config.created_time,
                updated_time=datetime.now().isoformat()
            )
            
            # 验证配置
            is_valid, error_msg = config.validate()
            if not is_valid:
                messagebox.showerror("错误", f"配置验证失败: {error_msg}")
                return
            
            # 检查名称重复
            existing_names = [c.name for c in self.configs if c != self.current_config]
            if config.name in existing_names:
                messagebox.showerror("错误", "配置名称已存在")
                return
            
            # 保存配置
            if self.current_config:
                # 更新现有配置
                for i, c in enumerate(self.configs):
                    if c == self.current_config:
                        self.configs[i] = config
                        break
            else:
                # 添加新配置
                self.configs.append(config)
            
            self.config_manager.save_database_configs(self.configs)
            self._refresh_config_list()
            
            self.result = True
            messagebox.showinfo("成功", "配置保存成功")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _apply_config(self):
        """应用配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return
        
        try:
            self.config_manager.set_current_database_config(self.current_config.name)
            messagebox.showinfo("成功", f"已设置 '{self.current_config.name}' 为当前配置")
        except Exception as e:
            logger.error(f"应用配置失败: {e}")
            messagebox.showerror("错误", f"应用配置失败: {e}")
    
    def _cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()