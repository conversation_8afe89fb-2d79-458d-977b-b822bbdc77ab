#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL脚本管理器界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from typing import Dict, Any, Optional

from ..config.config_manager import ConfigManager
from ..database.manager import DatabaseManager
from ..utils.file_utils import FileUtils
from ..utils.directory import get_project_root

logger = logging.getLogger(__name__)


class SqlScriptManager:
    """SQL脚本管理器"""
    
    def __init__(self, parent, config_manager: ConfigManager, db_manager: DatabaseManager):
        self.parent = parent
        self.config_manager = config_manager
        self.db_manager = db_manager
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 脚本数据
        self.scripts = {}
        self.current_script_id = None
        
        # 界面变量
        self.script_name_var = tk.StringVar()
        self.script_category_var = tk.StringVar()
        self.script_description_var = tk.StringVar()
        
        self._create_widgets()
        self._load_scripts()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建水平分割面板
        paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧：脚本列表
        left_frame = ttk.LabelFrame(paned, text="SQL脚本列表")
        paned.add(left_frame, weight=1)
        
        self._create_script_list(left_frame)
        
        # 右侧：脚本编辑器
        right_frame = ttk.LabelFrame(paned, text="脚本编辑器")
        paned.add(right_frame, weight=3)
        
        self._create_script_editor(right_frame)
    
    def _create_script_list(self, parent):
        """创建脚本列表"""
        # 搜索框
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        search_entry.bind('<KeyRelease>', self._on_search)
        
        # 分类过滤
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(filter_frame, text="分类:").pack(side=tk.LEFT)
        self.category_filter_var = tk.StringVar(value="全部")
        category_combo = ttk.Combobox(filter_frame, textvariable=self.category_filter_var, 
                                     state="readonly", width=15)
        category_combo.pack(side=tk.LEFT, padx=5)
        category_combo.bind("<<ComboboxSelected>>", self._on_category_filter)
        
        # 脚本树形列表
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ("名称", "分类", "更新时间")
        self.script_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.script_tree.heading(col, text=col)
            if col == "名称":
                self.script_tree.column(col, width=150)
            elif col == "分类":
                self.script_tree.column(col, width=80)
            else:
                self.script_tree.column(col, width=120)
        
        # 滚动条
        script_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.script_tree.yview)
        self.script_tree.configure(yscrollcommand=script_scrollbar.set)
        
        self.script_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        script_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.script_tree.bind("<<TreeviewSelect>>", self._on_script_select)
        self.script_tree.bind("<Double-1>", self._on_script_double_click)
        
        # 脚本操作按钮
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="新建", command=self._new_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="复制", command=self._copy_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=self._delete_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="导入", command=self._import_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="导出", command=self._export_script).pack(side=tk.LEFT, padx=2)
    
    def _create_script_editor(self, parent):
        """创建脚本编辑器"""
        # 脚本信息框架
        info_frame = ttk.LabelFrame(parent, text="脚本信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行：名称和分类
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="名称:").pack(side=tk.LEFT)
        name_entry = ttk.Entry(row1, textvariable=self.script_name_var, width=20)
        name_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(row1, text="分类:").pack(side=tk.LEFT, padx=(20, 0))
        category_combo = ttk.Combobox(row1, textvariable=self.script_category_var, 
                                     values=["销售", "财务", "人事", "库存", "客户", "生产", "质量", "采购"], 
                                     width=15)
        category_combo.pack(side=tk.LEFT, padx=5)
        
        # 第二行：描述
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="描述:").pack(side=tk.LEFT)
        desc_entry = ttk.Entry(row2, textvariable=self.script_description_var)
        desc_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # SQL编辑器框架
        editor_frame = ttk.LabelFrame(parent, text="SQL脚本")
        editor_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        toolbar = ttk.Frame(editor_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(toolbar, text="执行", command=self._execute_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="格式化", command=self._format_sql).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="清空", command=self._clear_editor).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(toolbar, text="保存", command=self._save_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="另存为", command=self._save_as_script).pack(side=tk.LEFT, padx=2)
        
        # 执行状态
        self.exec_status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(toolbar, textvariable=self.exec_status_var, foreground="blue")
        status_label.pack(side=tk.RIGHT, padx=10)
        
        # SQL文本编辑器
        editor_text_frame = ttk.Frame(editor_frame)
        editor_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.sql_text = tk.Text(editor_text_frame, wrap=tk.NONE, font=("Consolas", 10))
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(editor_text_frame, orient=tk.VERTICAL, command=self.sql_text.yview)
        h_scrollbar = ttk.Scrollbar(editor_text_frame, orient=tk.HORIZONTAL, command=self.sql_text.xview)
        self.sql_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.sql_text.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        editor_text_frame.grid_rowconfigure(0, weight=1)
        editor_text_frame.grid_columnconfigure(0, weight=1)
        
        # 结果显示框架
        result_frame = ttk.LabelFrame(parent, text="执行结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 结果选项卡
        result_notebook = ttk.Notebook(result_frame)
        result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 数据结果选项卡
        data_frame = ttk.Frame(result_notebook)
        result_notebook.add(data_frame, text="查询结果")
        
        self.result_tree = ttk.Treeview(data_frame, show="headings")
        result_v_scroll = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        result_h_scroll = ttk.Scrollbar(data_frame, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=result_v_scroll.set, xscrollcommand=result_h_scroll.set)
        
        self.result_tree.grid(row=0, column=0, sticky="nsew")
        result_v_scroll.grid(row=0, column=1, sticky="ns")
        result_h_scroll.grid(row=1, column=0, sticky="ew")
        
        data_frame.grid_rowconfigure(0, weight=1)
        data_frame.grid_columnconfigure(0, weight=1)
        
        # 消息日志选项卡
        log_frame = ttk.Frame(result_notebook)
        result_notebook.add(log_frame, text="执行日志")
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=8, state=tk.DISABLED)
        log_scroll = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scroll.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _load_scripts(self):
        """加载SQL脚本"""
        try:
            scripts_file = get_project_root() / "scripts" / "sql_scripts.json"
            data = FileUtils.read_json(scripts_file)
            self.scripts = data.get('scripts', {})
            
            self._refresh_script_list()
            self._update_category_filter()
            
            logger.info(f"加载了 {len(self.scripts)} 个SQL脚本")
            
        except Exception as e:
            logger.error(f"加载SQL脚本失败: {e}")
            messagebox.showerror("错误", f"加载SQL脚本失败: {e}")
    
    def _refresh_script_list(self):
        """刷新脚本列表"""
        # 清空现有项目
        for item in self.script_tree.get_children():
            self.script_tree.delete(item)
        
        # 获取过滤条件
        search_text = self.search_var.get().lower()
        category_filter = self.category_filter_var.get()
        
        # 添加脚本项目
        for script_id, script_data in self.scripts.items():
            name = script_data.get('name', script_id)
            category = script_data.get('category', '未分类')
            updated_time = script_data.get('updated_time', '')
            
            # 应用过滤条件
            if search_text and search_text not in name.lower() and search_text not in category.lower():
                continue
            
            if category_filter != "全部" and category != category_filter:
                continue
            
            self.script_tree.insert("", tk.END, values=(name, category, updated_time))
    
    def _update_category_filter(self):
        """更新分类过滤器"""
        categories = set()
        for script_data in self.scripts.values():
            categories.add(script_data.get('category', '未分类'))
        
        category_list = ["全部"] + sorted(list(categories))
        
        # 更新组合框
        for widget in self.frame.winfo_children():
            if isinstance(widget, ttk.PanedWindow):
                for child in widget.winfo_children():
                    if "脚本列表" in str(child):
                        for subchild in child.winfo_children():
                            if isinstance(subchild, ttk.Frame):
                                for combo in subchild.winfo_children():
                                    if isinstance(combo, ttk.Combobox):
                                        combo['values'] = category_list
                                        break
    
    def _on_search(self, event):
        """搜索事件"""
        self._refresh_script_list()
    
    def _on_category_filter(self, event):
        """分类过滤事件"""
        self._refresh_script_list()
    
    def _on_script_select(self, event):
        """脚本选择事件"""
        selection = self.script_tree.selection()
        if not selection:
            return
        
        item = self.script_tree.item(selection[0])
        script_name = item['values'][0]
        
        # 查找脚本ID
        for script_id, script_data in self.scripts.items():
            if script_data.get('name', script_id) == script_name:
                self.current_script_id = script_id
                self._load_script_to_editor(script_data)
                break
    
    def _on_script_double_click(self, event):
        """脚本双击事件"""
        self._execute_script()
    
    def _load_script_to_editor(self, script_data: Dict[str, Any]):
        """加载脚本到编辑器"""
        self.script_name_var.set(script_data.get('name', ''))
        self.script_category_var.set(script_data.get('category', ''))
        self.script_description_var.set(script_data.get('description', ''))
        
        # 加载SQL内容
        self.sql_text.delete(1.0, tk.END)
        self.sql_text.insert(1.0, script_data.get('sql', ''))
        
        self.exec_status_var.set("脚本已加载")
    
    def _new_script(self):
        """新建脚本"""
        self.current_script_id = None
        self._clear_editor()
        self.exec_status_var.set("新建脚本")
    
    def _copy_script(self):
        """复制脚本"""
        if not self.current_script_id:
            messagebox.showwarning("警告", "请先选择一个脚本")
            return
        
        try:
            # 获取当前脚本数据
            script_data = self.scripts[self.current_script_id].copy()
            script_data['name'] = f"{script_data['name']}_副本"
            
            # 生成新ID
            import uuid
            new_id = str(uuid.uuid4())
            
            # 添加到脚本列表
            self.scripts[new_id] = script_data
            self._save_scripts()
            self._refresh_script_list()
            
            messagebox.showinfo("成功", "脚本复制成功")
            
        except Exception as e:
            logger.error(f"复制脚本失败: {e}")
            messagebox.showerror("错误", f"复制脚本失败: {e}")
    
    def _delete_script(self):
        """删除脚本"""
        if not self.current_script_id:
            messagebox.showwarning("警告", "请先选择一个脚本")
            return
        
        script_name = self.scripts[self.current_script_id].get('name', '未命名')
        if messagebox.askyesno("确认", f"确定要删除脚本 '{script_name}' 吗？"):
            try:
                del self.scripts[self.current_script_id]
                self._save_scripts()
                self._refresh_script_list()
                self._clear_editor()
                self.current_script_id = None
                
                messagebox.showinfo("成功", "脚本删除成功")
                
            except Exception as e:
                logger.error(f"删除脚本失败: {e}")
                messagebox.showerror("错误", f"删除脚本失败: {e}")
    
    def _import_script(self):
        """导入脚本"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择SQL脚本文件",
                filetypes=[("SQL文件", "*.sql"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # 创建新脚本
                import uuid
                from datetime import datetime
                
                script_id = str(uuid.uuid4())
                script_name = Path(file_path).stem
                
                script_data = {
                    'name': script_name,
                    'category': '导入',
                    'description': f'从文件导入: {Path(file_path).name}',
                    'sql': sql_content,
                    'created_time': datetime.now().isoformat(),
                    'updated_time': datetime.now().isoformat()
                }
                
                self.scripts[script_id] = script_data
                self._save_scripts()
                self._refresh_script_list()
                
                messagebox.showinfo("成功", f"脚本 '{script_name}' 导入成功")
                
        except Exception as e:
            logger.error(f"导入脚本失败: {e}")
            messagebox.showerror("错误", f"导入脚本失败: {e}")
    
    def _export_script(self):
        """导出脚本"""
        if not self.current_script_id:
            messagebox.showwarning("警告", "请先选择一个脚本")
            return
        
        try:
            script_data = self.scripts[self.current_script_id]
            script_name = script_data.get('name', '未命名')
            
            file_path = filedialog.asksaveasfilename(
                title="保存SQL脚本",
                defaultextension=".sql",
                filetypes=[("SQL文件", "*.sql"), ("文本文件", "*.txt")],
                initialvalue=f"{script_name}.sql"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(script_data.get('sql', ''))
                
                messagebox.showinfo("成功", f"脚本已导出到: {file_path}")
                
        except Exception as e:
            logger.error(f"导出脚本失败: {e}")
            messagebox.showerror("错误", f"导出脚本失败: {e}")
    
    def _execute_script(self):
        """执行脚本"""
        try:
            sql_content = self.sql_text.get(1.0, tk.END).strip()
            if not sql_content:
                messagebox.showwarning("警告", "请输入SQL脚本")
                return
            
            self.exec_status_var.set("正在执行...")
            self._log_message("开始执行SQL脚本")
            
            # 清空结果显示
            self._clear_results()
            
            # TODO: 实际执行SQL查询
            # result = self.db_manager.execute_query(sql_content)
            # self._display_results(result)
            
            # 模拟执行结果
            self._display_mock_results()
            
            self.exec_status_var.set("执行完成")
            self._log_message("SQL脚本执行完成")
            
        except Exception as e:
            logger.error(f"执行脚本失败: {e}")
            self.exec_status_var.set("执行失败")
            self._log_message(f"执行失败: {e}", "ERROR")
            messagebox.showerror("错误", f"执行脚本失败: {e}")
    
    def _format_sql(self):
        """格式化SQL"""
        try:
            sql_content = self.sql_text.get(1.0, tk.END).strip()
            if not sql_content:
                return
            
            # 简单的SQL格式化
            formatted_sql = self._simple_sql_format(sql_content)
            
            self.sql_text.delete(1.0, tk.END)
            self.sql_text.insert(1.0, formatted_sql)
            
            self.exec_status_var.set("SQL已格式化")
            
        except Exception as e:
            logger.error(f"格式化SQL失败: {e}")
            messagebox.showerror("错误", f"格式化SQL失败: {e}")
    
    def _simple_sql_format(self, sql: str) -> str:
        """简单的SQL格式化"""
        keywords = ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'ORDER BY', 'HAVING', 
                   'INSERT', 'UPDATE', 'DELETE', 'JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN']
        
        lines = []
        for line in sql.split('\n'):
            line = line.strip()
            if line:
                for keyword in keywords:
                    if line.upper().startswith(keyword):
                        line = keyword + line[len(keyword):]
                        break
                lines.append(line)
        
        return '\n'.join(lines)
    
    def _clear_editor(self):
        """清空编辑器"""
        self.script_name_var.set("")
        self.script_category_var.set("")
        self.script_description_var.set("")
        self.sql_text.delete(1.0, tk.END)
        self._clear_results()
        self.exec_status_var.set("就绪")
    
    def _save_script(self):
        """保存脚本"""
        try:
            name = self.script_name_var.get().strip()
            if not name:
                messagebox.showwarning("警告", "请输入脚本名称")
                return
            
            sql_content = self.sql_text.get(1.0, tk.END).strip()
            if not sql_content:
                messagebox.showwarning("警告", "请输入SQL内容")
                return
            
            from datetime import datetime
            
            if self.current_script_id:
                # 更新现有脚本
                script_data = self.scripts[self.current_script_id]
                script_data.update({
                    'name': name,
                    'category': self.script_category_var.get(),
                    'description': self.script_description_var.get(),
                    'sql': sql_content,
                    'updated_time': datetime.now().isoformat()
                })
            else:
                # 创建新脚本
                import uuid
                script_id = str(uuid.uuid4())
                
                script_data = {
                    'name': name,
                    'category': self.script_category_var.get(),
                    'description': self.script_description_var.get(),
                    'sql': sql_content,
                    'created_time': datetime.now().isoformat(),
                    'updated_time': datetime.now().isoformat()
                }
                
                self.scripts[script_id] = script_data
                self.current_script_id = script_id
            
            self._save_scripts()
            self._refresh_script_list()
            self._update_category_filter()
            
            messagebox.showinfo("成功", "脚本保存成功")
            self.exec_status_var.set("脚本已保存")
            
        except Exception as e:
            logger.error(f"保存脚本失败: {e}")
            messagebox.showerror("错误", f"保存脚本失败: {e}")
    
    def _save_as_script(self):
        """另存为脚本"""
        self.current_script_id = None
        self._save_script()
    
    def _save_scripts(self):
        """保存脚本到文件"""
        try:
            scripts_file = get_project_root() / "scripts" / "sql_scripts.json"
            data = {'scripts': self.scripts}
            FileUtils.write_json(scripts_file, data)
            
        except Exception as e:
            logger.error(f"保存脚本文件失败: {e}")
            raise
    
    def _clear_results(self):
        """清空结果显示"""
        # 清空结果树
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 清空列定义
        self.result_tree["columns"] = ()
        self.result_tree["show"] = "headings"
    
    def _display_mock_results(self):
        """显示模拟结果"""
        # 模拟查询结果
        columns = ["ID", "姓名", "部门", "薪资"]
        data = [
            (1, "张三", "销售部", 8000),
            (2, "李四", "技术部", 12000),
            (3, "王五", "财务部", 9000)
        ]
        
        self._display_results(columns, data)
    
    def _display_results(self, columns, data):
        """显示查询结果"""
        # 设置列
        self.result_tree["columns"] = columns
        self.result_tree["show"] = "headings"
        
        for col in columns:
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=100)
        
        # 添加数据
        for row in data:
            self.result_tree.insert("", tk.END, values=row)
        
        self._log_message(f"查询完成，返回 {len(data)} 行数据")
    
    def _log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def execute_current_script(self):
        """执行当前脚本（供外部调用）"""
        self._execute_script()