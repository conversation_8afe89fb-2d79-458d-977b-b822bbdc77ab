#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用设置对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
from typing import Dict, Any

from ..config.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class SettingsDialog:
    """应用设置对话框"""
    
    def __init__(self, parent, config_manager: ConfigManager):
        self.parent = parent
        self.config_manager = config_manager
        self.dialog = None
        self.result = False
        
        # 设置变量
        self.theme_var = tk.StringVar(value="default")
        self.auto_backup_var = tk.BooleanVar(value=True)
        self.backup_days_var = tk.IntVar(value=30)
        self.log_level_var = tk.StringVar(value="INFO")
        self.excel_start_row_var = tk.IntVar(value=2)
        self.date_format_var = tk.StringVar(value="%Y-%m-%d")
        self.output_dir_var = tk.StringVar(value="output")
        self.window_geometry_var = tk.StringVar(value="1200x800")
        
        # 路径变量
        self.config_dir_var = tk.StringVar()
        self.template_dir_var = tk.StringVar()
        self.output_dir_path_var = tk.StringVar()
        self.log_dir_var = tk.StringVar()
    
    def show(self) -> bool:
        """显示对话框"""
        self._create_dialog()
        self._load_settings()
        
        # 模态显示
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.wait_window()
        
        return self.result
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("应用设置")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 常规设置选项卡
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="常规设置")
        self._create_general_settings(general_frame)
        
        # 界面设置选项卡
        ui_frame = ttk.Frame(notebook)
        notebook.add(ui_frame, text="界面设置")
        self._create_ui_settings(ui_frame)
        
        # 路径设置选项卡
        path_frame = ttk.Frame(notebook)
        notebook.add(path_frame, text="路径设置")
        self._create_path_settings(path_frame)
        
        # 高级设置选项卡
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="高级设置")
        self._create_advanced_settings(advanced_frame)
        
        # 底部按钮
        self._create_buttons(main_frame)
    
    def _create_general_settings(self, parent):
        """创建常规设置"""
        # 滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 备份设置
        backup_frame = ttk.LabelFrame(scrollable_frame, text="备份设置")
        backup_frame.pack(fill=tk.X, padx=5, pady=5)
        
        auto_backup_check = ttk.Checkbutton(backup_frame, text="启用自动备份", 
                                           variable=self.auto_backup_var)
        auto_backup_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(backup_frame, text="备份保留天数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        backup_days_spin = ttk.Spinbox(backup_frame, from_=1, to=365, 
                                      textvariable=self.backup_days_var, width=10)
        backup_days_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 日志设置
        log_frame = ttk.LabelFrame(scrollable_frame, text="日志设置")
        log_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(log_frame, text="日志级别:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        log_level_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var,
                                      values=["DEBUG", "INFO", "WARNING", "ERROR"],
                                      state="readonly", width=15)
        log_level_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Excel设置
        excel_frame = ttk.LabelFrame(scrollable_frame, text="Excel设置")
        excel_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(excel_frame, text="默认起始行:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        excel_start_spin = ttk.Spinbox(excel_frame, from_=1, to=100,
                                      textvariable=self.excel_start_row_var, width=10)
        excel_start_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(excel_frame, text="日期格式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        date_format_combo = ttk.Combobox(excel_frame, textvariable=self.date_format_var,
                                        values=["%Y-%m-%d", "%Y/%m/%d", "%d/%m/%Y", "%m/%d/%Y"],
                                        width=15)
        date_format_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _create_ui_settings(self, parent):
        """创建界面设置"""
        # 主题设置
        theme_frame = ttk.LabelFrame(parent, text="主题设置")
        theme_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(theme_frame, text="界面主题:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=["default", "clam", "alt", "classic"],
                                  state="readonly", width=15)
        theme_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 窗口设置
        window_frame = ttk.LabelFrame(parent, text="窗口设置")
        window_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(window_frame, text="默认窗口大小:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        geometry_combo = ttk.Combobox(window_frame, textvariable=self.window_geometry_var,
                                     values=["800x600", "1024x768", "1200x800", "1440x900", "1920x1080"],
                                     width=15)
        geometry_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 预览区域
        preview_frame = ttk.LabelFrame(parent, text="主题预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 示例组件
        ttk.Label(preview_frame, text="这是一个标签示例").pack(pady=5)
        ttk.Button(preview_frame, text="这是一个按钮示例").pack(pady=2)
        
        progress = ttk.Progressbar(preview_frame, length=200, mode='determinate', value=50)
        progress.pack(pady=5)
        
        # 绑定主题变化事件
        theme_combo.bind("<<ComboboxSelected>>", self._on_theme_changed)
    
    def _create_path_settings(self, parent):
        """创建路径设置"""
        # 路径配置
        paths = [
            ("配置目录:", self.config_dir_var),
            ("模板目录:", self.template_dir_var),
            ("输出目录:", self.output_dir_path_var),
            ("日志目录:", self.log_dir_var)
        ]
        
        for i, (label_text, var) in enumerate(paths):
            frame = ttk.Frame(parent)
            frame.pack(fill=tk.X, padx=5, pady=2)
            
            ttk.Label(frame, text=label_text, width=12).pack(side=tk.LEFT)
            
            entry = ttk.Entry(frame, textvariable=var, state="readonly")
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            
            ttk.Button(frame, text="浏览", 
                      command=lambda v=var: self._browse_directory(v)).pack(side=tk.RIGHT)
        
        # 路径操作按钮
        path_btn_frame = ttk.Frame(parent)
        path_btn_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(path_btn_frame, text="重置为默认", command=self._reset_paths).pack(side=tk.LEFT, padx=5)
        ttk.Button(path_btn_frame, text="打开配置目录", command=self._open_config_dir).pack(side=tk.LEFT, padx=5)
        
        # 路径说明
        info_frame = ttk.LabelFrame(parent, text="路径说明")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        info_content = """
配置目录: 存储数据库配置、映射关系等配置文件
模板目录: 存储Excel模板文件
输出目录: 存储处理后的Excel文件
日志目录: 存储应用程序日志文件

注意: 修改路径后需要重启应用程序才能生效
        """
        
        info_text.config(state=tk.NORMAL)
        info_text.insert(1.0, info_content.strip())
        info_text.config(state=tk.DISABLED)
    
    def _create_advanced_settings(self, parent):
        """创建高级设置"""
        # 性能设置
        performance_frame = ttk.LabelFrame(parent, text="性能设置")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(performance_frame, text="数据库连接超时(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        timeout_spin = ttk.Spinbox(performance_frame, from_=5, to=300, width=10)
        timeout_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(performance_frame, text="批量处理大小:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        batch_spin = ttk.Spinbox(performance_frame, from_=100, to=10000, width=10)
        batch_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 调试设置
        debug_frame = ttk.LabelFrame(parent, text="调试设置")
        debug_frame.pack(fill=tk.X, padx=5, pady=5)
        
        debug_mode_var = tk.BooleanVar()
        ttk.Checkbutton(debug_frame, text="启用调试模式", variable=debug_mode_var).pack(anchor=tk.W, padx=5, pady=2)
        
        verbose_log_var = tk.BooleanVar()
        ttk.Checkbutton(debug_frame, text="详细日志输出", variable=verbose_log_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # 重置设置
        reset_frame = ttk.LabelFrame(parent, text="重置设置")
        reset_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(reset_frame, text="重置所有设置", command=self._reset_all_settings).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(reset_frame, text="清理缓存", command=self._clear_cache).pack(side=tk.LEFT, padx=5, pady=5)
    
    def _create_buttons(self, parent):
        """创建底部按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="确定", command=self._save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self._cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="应用", command=self._apply_settings).pack(side=tk.RIGHT, padx=5)
    
    def _load_settings(self):
        """加载设置"""
        try:
            settings = self.config_manager.load_app_settings()
            
            self.theme_var.set(settings.get('theme', 'default'))
            self.auto_backup_var.set(settings.get('auto_backup', True))
            self.backup_days_var.set(settings.get('backup_days', 30))
            self.log_level_var.set(settings.get('log_level', 'INFO'))
            self.excel_start_row_var.set(settings.get('excel_start_row', 2))
            self.date_format_var.set(settings.get('date_format', '%Y-%m-%d'))
            self.window_geometry_var.set(settings.get('window_geometry', '1200x800'))
            
            # 加载路径设置
            from ..utils.directory import get_project_root
            root = get_project_root()
            
            self.config_dir_var.set(str(root / "config"))
            self.template_dir_var.set(str(root / "templates"))
            self.output_dir_path_var.set(str(root / "output"))
            self.log_dir_var.set(str(root / "logs"))
            
        except Exception as e:
            logger.error(f"加载设置失败: {e}")
            messagebox.showerror("错误", f"加载设置失败: {e}")
    
    def _save_settings(self):
        """保存设置"""
        try:
            settings = {
                'theme': self.theme_var.get(),
                'auto_backup': self.auto_backup_var.get(),
                'backup_days': self.backup_days_var.get(),
                'log_level': self.log_level_var.get(),
                'excel_start_row': self.excel_start_row_var.get(),
                'date_format': self.date_format_var.get(),
                'window_geometry': self.window_geometry_var.get()
            }
            
            self.config_manager.save_app_settings(settings)
            self.result = True
            messagebox.showinfo("成功", "设置保存成功")
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 应用主题
            style = ttk.Style()
            style.theme_use(self.theme_var.get())
            
            messagebox.showinfo("成功", "设置已应用")
            
        except Exception as e:
            logger.error(f"应用设置失败: {e}")
            messagebox.showerror("错误", f"应用设置失败: {e}")
    
    def _cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()
    
    def _on_theme_changed(self, event):
        """主题改变事件"""
        try:
            style = ttk.Style()
            style.theme_use(self.theme_var.get())
        except Exception as e:
            logger.error(f"切换主题失败: {e}")
    
    def _browse_directory(self, var):
        """浏览目录"""
        directory = filedialog.askdirectory(title="选择目录")
        if directory:
            var.set(directory)
    
    def _reset_paths(self):
        """重置路径为默认值"""
        if messagebox.askyesno("确认", "确定要重置所有路径为默认值吗？"):
            from ..utils.directory import get_project_root
            root = get_project_root()
            
            self.config_dir_var.set(str(root / "config"))
            self.template_dir_var.set(str(root / "templates"))
            self.output_dir_path_var.set(str(root / "output"))
            self.log_dir_var.set(str(root / "logs"))
    
    def _open_config_dir(self):
        """打开配置目录"""
        import os
        import subprocess
        
        config_dir = self.config_dir_var.get()
        if Path(config_dir).exists():
            if os.name == 'nt':  # Windows
                os.startfile(config_dir)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', config_dir])
    
    def _reset_all_settings(self):
        """重置所有设置"""
        if messagebox.askyesno("确认", "确定要重置所有设置为默认值吗？这将清除所有自定义配置！"):
            try:
                # 重置为默认值
                self.theme_var.set("default")
                self.auto_backup_var.set(True)
                self.backup_days_var.set(30)
                self.log_level_var.set("INFO")
                self.excel_start_row_var.set(2)
                self.date_format_var.set("%Y-%m-%d")
                self.window_geometry_var.set("1200x800")
                
                messagebox.showinfo("成功", "所有设置已重置为默认值")
                
            except Exception as e:
                logger.error(f"重置设置失败: {e}")
                messagebox.showerror("错误", f"重置设置失败: {e}")
    
    def _clear_cache(self):
        """清理缓存"""
        if messagebox.askyesno("确认", "确定要清理所有缓存文件吗？"):
            try:
                # TODO: 实现缓存清理逻辑
                messagebox.showinfo("成功", "缓存清理完成")
                
            except Exception as e:
                logger.error(f"清理缓存失败: {e}")
                messagebox.showerror("错误", f"清理缓存失败: {e}")