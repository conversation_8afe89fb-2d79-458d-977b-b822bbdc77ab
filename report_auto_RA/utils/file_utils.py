"""
文件操作工具
"""

import os
import shutil
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Any, Dict
from ..exceptions.app_exceptions import FileOperationException

logger = logging.getLogger(__name__)


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def read_json(file_path: Path) -> Dict[str, Any]:
        """读取JSON文件"""
        try:
            if not file_path.exists():
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取JSON文件失败 {file_path}: {e}")
            raise FileOperationException(f"读取JSON文件失败: {e}")
    
    @staticmethod
    def write_json(file_path: Path, data: Dict[str, Any]):
        """写入JSON文件"""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"JSON文件写入成功: {file_path}")
        except Exception as e:
            logger.error(f"写入JSON文件失败 {file_path}: {e}")
            raise FileOperationException(f"写入JSON文件失败: {e}")
    
    @staticmethod
    def backup_file(file_path: Path, backup_dir: Path = None) -> Path:
        """备份文件"""
        try:
            if not file_path.exists():
                raise FileOperationException(f"要备份的文件不存在: {file_path}")
            
            if backup_dir is None:
                backup_dir = Path("backup")
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(file_path, backup_path)
            logger.info(f"文件备份成功: {file_path} -> {backup_path}")
            
            return backup_path
        except Exception as e:
            logger.error(f"文件备份失败 {file_path}: {e}")
            raise FileOperationException(f"文件备份失败: {e}")
    
    @staticmethod
    def cleanup_old_files(directory: Path, days: int = 30):
        """清理旧文件"""
        try:
            if not directory.exists():
                return
            
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            
            for file_path in directory.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.debug(f"删除旧文件: {file_path}")
            
            logger.info(f"清理完成，删除了 {days} 天前的文件")
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
    
    @staticmethod
    def get_file_size(file_path: Path) -> str:
        """获取文件大小（人类可读格式）"""
        try:
            if not file_path.exists():
                return "0 B"
            
            size = file_path.stat().st_size
            
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            
            return f"{size:.1f} TB"
        except Exception:
            return "未知"