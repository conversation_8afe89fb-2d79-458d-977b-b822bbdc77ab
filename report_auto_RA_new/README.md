# Report Auto RA - Excel数据库查询填充工具

## 📋 项目简介
一个基于Python的桌面应用程序，支持通过可视化界面配置数据库连接，读取Excel模板并根据SQL查询结果自动填充数据。采用模块化设计，易于扩展和维护。

## ✨ 功能特性
- 🔗 **多数据库支持** - Oracle、MySQL数据库连接
- 📊 **Excel自动填充** - 基于模板的数据自动填充
- ⚙️ **可视化配置** - 友好的配置管理界面
- 📝 **SQL脚本管理** - 脚本存储、版本控制和执行
- 🔒 **安全加密** - 密码加密存储
- 🎨 **现代UI** - 基于tkinter的现代化界面
- 📈 **报表生成** - 多格式报表导出
- 🧪 **完整测试** - 单元测试和集成测试覆盖

## 🛠️ 技术栈
- **Python 3.8+** - 主要开发语言
- **tkinter** - GUI框架
- **openpyxl** - Excel文件处理
- **cx_Oracle** - Oracle数据库连接
- **PyMySQL** - MySQL数据库连接
- **cryptography** - 数据加密
- **pytest** - 测试框架

## 📁 项目结构
```
report_auto_RA/
├── 📄 main.py                      # 应用程序入口
├── 📄 requirements.txt             # Python依赖包
├── 📄 README.md                    # 项目说明文档
├── 📄 .env.example                 # 环境变量示例
├── 📄 .gitignore                   # Git忽略文件
│
├── 📁 app/                         # 应用程序核心
│   ├── app.py                      # 应用程序主类
│   ├── constants.py                # 全局常量
│   └── version.py                  # 版本信息
│
├── 📁 core/                        # 核心业务逻辑
│   ├── database/                   # 数据库相关
│   │   ├── models.py               # 数据模型
│   │   ├── manager.py              # 数据库管理器
│   │   ├── connection.py           # 连接管理
│   │   └── adapters/               # 数据库适配器
│   │       ├── base.py             # 基础适配器
│   │       ├── mysql.py            # MySQL适配器
│   │       ├── oracle.py           # Oracle适配器
│   │       └── factory.py          # 适配器工厂
│   ├── excel/                      # Excel处理
│   ├── scripts/                    # SQL脚本管理
│   └── reports/                    # 报表生成
│
├── 📁 ui/                          # 用户界面
│   ├── main_window.py              # 主窗口
│   ├── dialogs/                    # 对话框
│   ├── widgets/                    # 自定义控件
│   └── resources/                  # UI资源
│
├── 📁 config/                      # 配置管理
│   ├── manager.py                  # 配置管理器
│   ├── validator.py                # 配置验证器
│   ├── defaults.py                 # 默认配置
│   └── schemas/                    # 配置模式
│
├── 📁 utils/                       # 工具模块
├── 📁 exceptions/                  # 异常定义
├── 📁 data/                        # 数据目录
├── 📁 logs/                        # 日志目录
├── 📁 tests/                       # 测试代码
└── 📁 docs/                        # 文档目录
```

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- Windows/Linux/macOS 操作系统

### 安装步骤
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd report_auto_RA
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入实际配置
   ```

4. **运行程序**
   ```bash
   python main.py
   ```

## 🧪 测试

### 运行所有测试
```bash
python tests/test_runner.py
```

### 运行特定测试
```bash
python -m pytest tests/unit/core/ -v
python -m pytest tests/unit/utils/ -v
```

### 生成覆盖率报告
```bash
coverage run -m pytest tests/ -v
coverage report
coverage html
```

## 📖 开发指南

### 代码规范
- 遵循 PEP 8 代码风格
- 使用类型提示
- 编写完整的文档字符串
- 保持测试覆盖率 > 80%

### 添加新功能
1. 在对应模块创建功能代码
2. 编写单元测试
3. 更新文档
4. 提交代码审查

## 📝 版本历史
- **v1.0.0** - 初始版本，基础框架搭建
- **v1.1.0** - 项目结构重构，模块化设计
- **v1.2.0** - 完善测试体系，提高代码质量

## 🤝 贡献指南
欢迎提交 Issue 和 Pull Request！

## 📄 许可证
MIT License