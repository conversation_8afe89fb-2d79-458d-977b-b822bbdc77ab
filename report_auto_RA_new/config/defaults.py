"""
默认配置定义
"""

from pathlib import Path

# 应用程序默认配置
DEFAULT_APP_CONFIG = {
    "app": {
        "name": "Report Auto RA",
        "version": "1.0.0",
        "debug": False
    },
    "database": {
        "timeout": 30,
        "max_connections": 5,
        "retry_attempts": 3,
        "retry_delay": 1
    },
    "excel": {
        "max_rows": 10000,
        "max_columns": 100,
        "date_format": "%Y-%m-%d",
        "datetime_format": "%Y-%m-%d %H:%M:%S",
        "encoding": "utf-8"
    },
    "ui": {
        "theme": "default",
        "language": "zh_CN",
        "window_size": {
            "width": 1200,
            "height": 800
        },
        "auto_save": True,
        "auto_save_interval": 300
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file_max_size": 10485760,  # 10MB
        "backup_count": 5
    },
    "security": {
        "encrypt_passwords": True,
        "session_timeout": 3600
    }
}

# 数据库配置模板
DEFAULT_DATABASE_CONFIG = {
    "name": "",
    "db_type": "mysql",
    "host": "localhost",
    "port": 3306,
    "username": "",
    "password": "",
    "database": "",
    "description": "",
    "ssl_enabled": False,
    "connection_options": {}
}

# UI配置模板
DEFAULT_UI_CONFIG = {
    "main_window": {
        "geometry": "1200x800+100+100",
        "maximized": False,
        "splitter_sizes": [300, 900]
    },
    "dialogs": {
        "remember_size": True,
        "center_on_parent": True
    },
    "grid": {
        "row_height": 25,
        "show_grid_lines": True,
        "alternate_row_colors": True
    },
    "fonts": {
        "default": {
            "family": "Microsoft YaHei",
            "size": 9
        },
        "code": {
            "family": "Consolas",
            "size": 10
        }
    }
}

# 文件路径配置
DEFAULT_PATHS = {
    "data_dir": "data",
    "config_dir": "data/config",
    "templates_dir": "data/templates",
    "output_dir": "data/output",
    "backup_dir": "data/backup",
    "cache_dir": "data/cache",
    "logs_dir": "logs"
}

def get_default_config(config_type: str = "app"):
    """
    获取默认配置
    
    Args:
        config_type: 配置类型 ('app', 'database', 'ui')
        
    Returns:
        dict: 默认配置字典
    """
    configs = {
        "app": DEFAULT_APP_CONFIG,
        "database": DEFAULT_DATABASE_CONFIG,
        "ui": DEFAULT_UI_CONFIG,
        "paths": DEFAULT_PATHS
    }
    
    return configs.get(config_type, {}).copy()

def get_config_file_path(config_name: str, base_dir: Path = None) -> Path:
    """
    获取配置文件路径
    
    Args:
        config_name: 配置文件名
        base_dir: 基础目录
        
    Returns:
        Path: 配置文件路径
    """
    if base_dir is None:
        base_dir = Path.cwd()
    
    config_dir = base_dir / "data" / "config"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    return config_dir / f"{config_name}.json"
