"""
MySQL数据库适配器
"""

import pymysql
import logging
from typing import List, Dict, Any
from .base import BaseAdapter
from ....exceptions.database import DatabaseException
from ....utils.encryption import decrypt_password

logger = logging.getLogger(__name__)


class MySQLAdapter(BaseAdapter):
    """MySQL数据库适配器"""
    
    def connect(self):
        """建立MySQL连接"""
        try:
            password = decrypt_password(self.config.password) if self.config.password else ""
            
            self.connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=password,
                database=self.config.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
            logger.info(f"MySQL连接建立成功: {self.config.get_connection_string()}")
            
        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            raise DatabaseException(f"MySQL连接失败: {e}")
    
    def test_connection(self) -> bool:
        """测试MySQL连接"""
        try:
            self.connect()
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1 as test")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"MySQL连接测试失败: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()
                self.connection = None
    
    def execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行MySQL查询"""
        if not self.connection:
            self.connect()
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()
                
                # 确保结果是字典列表
                if results and isinstance(results, (list, tuple)):
                    return list(results)
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"MySQL查询执行失败: {e}")
            raise DatabaseException(f"查询执行失败: {e}")
    
    def close(self):
        """关闭MySQL连接"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("MySQL连接已关闭")
            except Exception as e:
                logger.warning(f"关闭MySQL连接时出现警告: {e}")
            finally:
                self.connection = None