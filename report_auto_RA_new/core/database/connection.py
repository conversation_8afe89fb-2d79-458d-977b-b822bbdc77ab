"""
数据库连接管理
"""

import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager
from .adapters.factory import AdapterFactory
from .models import DatabaseConfig

logger = logging.getLogger(__name__)

class ConnectionManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self._connections: Dict[str, Any] = {}
        self._current_config: Optional[DatabaseConfig] = None
    
    def set_config(self, config: DatabaseConfig):
        """设置数据库配置"""
        self._current_config = config
        logger.info(f"设置数据库配置: {config.get_display_name()}")
    
    def get_config(self) -> Optional[DatabaseConfig]:
        """获取当前数据库配置"""
        return self._current_config
    
    @contextmanager
    def get_connection(self, config: Optional[DatabaseConfig] = None):
        """
        获取数据库连接（上下文管理器）
        
        Args:
            config: 数据库配置，如果为None则使用当前配置
            
        Yields:
            数据库连接对象
        """
        use_config = config or self._current_config
        if not use_config:
            raise ValueError("没有可用的数据库配置")
        
        adapter = None
        try:
            adapter = AdapterFactory.create_adapter(use_config.db_type, use_config)
            connection = adapter.connect()
            logger.info(f"数据库连接成功: {use_config.get_display_name()}")
            yield connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
        finally:
            if adapter:
                adapter.close()
                logger.info("数据库连接已关闭")
    
    def test_connection(self, config: Optional[DatabaseConfig] = None) -> tuple[bool, str]:
        """
        测试数据库连接
        
        Args:
            config: 数据库配置
            
        Returns:
            tuple: (是否成功, 消息)
        """
        use_config = config or self._current_config
        if not use_config:
            return False, "没有可用的数据库配置"
        
        # 验证配置
        is_valid, error_msg = use_config.validate()
        if not is_valid:
            return False, f"配置验证失败: {error_msg}"
        
        try:
            adapter = AdapterFactory.create_adapter(use_config.db_type, use_config)
            result = adapter.test_connection()
            adapter.close()
            
            if result:
                logger.info(f"数据库连接测试成功: {use_config.get_connection_string()}")
                return True, "连接成功"
            else:
                return False, "连接失败"
                
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False, f"连接失败: {str(e)}"
    
    def execute_query(self, sql: str, params: Optional[Dict] = None, 
                     config: Optional[DatabaseConfig] = None) -> list:
        """
        执行SQL查询
        
        Args:
            sql: SQL语句
            params: 查询参数
            config: 数据库配置
            
        Returns:
            list: 查询结果
        """
        with self.get_connection(config) as connection:
            adapter = AdapterFactory.create_adapter(
                (config or self._current_config).db_type, 
                config or self._current_config
            )
            return adapter.execute_query(sql, params)
    
    def close_all_connections(self):
        """关闭所有连接"""
        for connection in self._connections.values():
            try:
                connection.close()
            except Exception as e:
                logger.warning(f"关闭连接时出错: {e}")
        
        self._connections.clear()
        logger.info("所有数据库连接已关闭")
