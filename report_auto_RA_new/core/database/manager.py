"""
数据库管理器
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from .models import DatabaseConfig
from .adapters.mysql import MySQLAdapter
from .adapters.oracle import OracleAdapter
from ...exceptions.database import DatabaseException

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.current_config: Optional[DatabaseConfig] = None
        self.current_adapter = None
        self._adapters = {
            'mysql': MySQLAdapter,
            'oracle': OracleAdapter
        }
    
    def set_config(self, config: DatabaseConfig):
        """设置当前数据库配置"""
        try:
            # 验证配置
            is_valid, error_msg = config.validate()
            if not is_valid:
                raise DatabaseException(f"配置验证失败: {error_msg}")
            
            # 关闭当前连接
            if self.current_adapter:
                self.current_adapter.close()
            
            # 创建新的适配器
            adapter_class = self._adapters.get(config.db_type)
            if not adapter_class:
                raise DatabaseException(f"不支持的数据库类型: {config.db_type}")
            
            self.current_adapter = adapter_class(config)
            self.current_config = config
            
            logger.info(f"数据库配置已设置: {config.get_connection_string()}")
            
        except Exception as e:
            logger.error(f"设置数据库配置失败: {e}")
            raise DatabaseException(f"设置数据库配置失败: {e}")
    
    def test_connection(self, config: DatabaseConfig = None) -> Tuple[bool, str]:
        """测试数据库连接"""
        test_config = config or self.current_config
        if not test_config:
            return False, "没有可用的数据库配置"
        
        # 验证配置
        is_valid, error_msg = test_config.validate()
        if not is_valid:
            return False, f"配置验证失败: {error_msg}"
        
        try:
            adapter_class = self._adapters.get(test_config.db_type)
            if not adapter_class:
                return False, f"不支持的数据库类型: {test_config.db_type}"
            
            test_adapter = adapter_class(test_config)
            result = test_adapter.test_connection()
            test_adapter.close()
            
            if result:
                logger.info(f"数据库连接测试成功: {test_config.get_connection_string()}")
                return True, "连接成功"
            else:
                return False, "连接失败"
                
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False, f"连接失败: {str(e)}"
    
    def execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行查询"""
        if not self.current_adapter:
            raise DatabaseException("没有可用的数据库连接")
        
        if not sql.strip():
            raise DatabaseException("SQL语句不能为空")
        
        try:
            logger.info(f"执行SQL查询: {sql[:100]}...")
            results = self.current_adapter.execute_query(sql)
            logger.info(f"查询完成，返回 {len(results)} 条记录")
            return results
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            raise DatabaseException(f"执行查询失败: {e}")
    
    def get_current_config(self) -> Optional[DatabaseConfig]:
        """获取当前配置"""
        return self.current_config
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.current_adapter is not None and self.current_config is not None
    
    def close(self):
        """关闭连接"""
        if self.current_adapter:
            self.current_adapter.close()
            self.current_adapter = None
            self.current_config = None
            logger.info("数据库连接已关闭")
