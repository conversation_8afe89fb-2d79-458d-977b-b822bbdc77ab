"""
数据库相关异常
"""

from .base import ReportAutoException

class DatabaseException(ReportAutoException):
    """数据库异常基类"""
    pass

class ConnectionError(DatabaseException):
    """数据库连接异常"""
    pass

class QueryError(DatabaseException):
    """查询执行异常"""
    pass

class ConfigurationError(DatabaseException):
    """数据库配置异常"""
    pass

class UnsupportedDatabaseError(DatabaseException):
    """不支持的数据库类型异常"""
    pass

class TimeoutError(DatabaseException):
    """数据库操作超时异常"""
    pass
