#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关于对话框
"""

import tkinter as tk
from tkinter import ttk
import webbrowser
from pathlib import Path

from .. import __version__, __author__, __description__, __app_name__


class AboutDialog:
    """关于对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.dialog = None
    
    def show(self):
        """显示对话框"""
        self._create_dialog()
        
        # 模态显示
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.wait_window()
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("关于")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 应用图标和名称
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 尝试加载应用图标
        icon_path = Path("assets/icons/app.png")
        if icon_path.exists():
            try:
                # TODO: 加载并显示图标
                pass
            except:
                pass
        
        # 应用名称
        app_name_label = ttk.Label(header_frame, text=__app_name__, 
                                  font=("Arial", 16, "bold"))
        app_name_label.pack()
        
        # 版本信息
        version_label = ttk.Label(header_frame, text=f"版本 {__version__}", 
                                 font=("Arial", 10))
        version_label.pack(pady=(5, 0))
        
        # 描述信息
        desc_frame = ttk.LabelFrame(main_frame, text="应用描述")
        desc_frame.pack(fill=tk.X, pady=(0, 15))
        
        desc_text = tk.Text(desc_frame, height=4, wrap=tk.WORD, 
                           state=tk.DISABLED, relief=tk.FLAT)
        desc_text.pack(fill=tk.X, padx=10, pady=10)
        
        description = f"""{__description__}

一个基于Python tkinter的桌面应用程序，支持通过配置连接Oracle/MySQL数据库，读取Excel模板并根据SQL查询结果填充数据。"""
        
        desc_text.config(state=tk.NORMAL)
        desc_text.insert(1.0, description)
        desc_text.config(state=tk.DISABLED)
        
        # 技术信息
        tech_frame = ttk.LabelFrame(main_frame, text="技术信息")
        tech_frame.pack(fill=tk.X, pady=(0, 15))
        
        tech_info = [
            ("开发者:", __author__),
            ("开发语言:", "Python 3.8+"),
            ("GUI框架:", "tkinter"),
            ("数据库支持:", "MySQL, Oracle"),
            ("Excel处理:", "openpyxl"),
            ("配置存储:", "JSON")
        ]
        
        for i, (label, value) in enumerate(tech_info):
            row_frame = ttk.Frame(tech_frame)
            row_frame.pack(fill=tk.X, padx=10, pady=2)
            
            ttk.Label(row_frame, text=label, width=12, anchor=tk.W).pack(side=tk.LEFT)
            ttk.Label(row_frame, text=value, anchor=tk.W).pack(side=tk.LEFT)
        
        # 链接和按钮
        link_frame = ttk.Frame(main_frame)
        link_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 项目链接（如果有的话）
        project_link = ttk.Label(link_frame, text="项目主页", 
                                foreground="blue", cursor="hand2")
        project_link.pack(side=tk.LEFT)
        project_link.bind("<Button-1>", lambda e: self._open_url("https://github.com/your-repo"))
        
        # 帮助链接
        help_link = ttk.Label(link_frame, text="使用帮助", 
                             foreground="blue", cursor="hand2")
        help_link.pack(side=tk.LEFT, padx=(20, 0))
        help_link.bind("<Button-1>", lambda e: self._show_help())
        
        # 底部按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)
        
        ttk.Button(btn_frame, text="系统信息", command=self._show_system_info).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="许可证", command=self._show_license).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(btn_frame, text="确定", command=self.dialog.destroy).pack(side=tk.RIGHT)
    
    def _open_url(self, url: str):
        """打开URL"""
        try:
            webbrowser.open(url)
        except Exception:
            pass
    
    def _show_help(self):
        """显示帮助"""