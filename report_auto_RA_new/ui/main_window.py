#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
from typing import Optional

from .dialogs.database_config import DatabaseConfigDialog
from .sql_script_manager import SqlScriptManager
from .dialogs.excel_manager import ExcelFileManager
from .dialogs.settings import SettingsDialog
from .dialogs.about import AboutDialog
from .progress_dialog import ProgressDialog
from ..config.manager import ConfigManager
from ..core.database.manager import DatabaseManager
from ..exceptions.app_exceptions import AppException

logger = logging.getLogger(__name__)


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        
        # 界面组件
        self.notebook = None
        self.status_bar = None
        self.progress_var = None
        self.status_var = None
        
        # 子窗口
        self.db_config_dialog = None
        self.sql_manager = None
        self.excel_manager = None
        
        self._setup_window()
        self._create_menu()
        self._create_toolbar()
        self._create_main_area()
        self._create_status_bar()
        self._load_settings()
        
        logger.info("主窗口初始化完成")
    
    def _setup_window(self):
        """设置窗口基本属性"""
        self.root.title("Report Auto RA - Excel数据库查询填充工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果存在）
        icon_path = Path("assets/icons/app.ico")
        if icon_path.exists():
            self.root.iconbitmap(str(icon_path))
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开Excel文件...", command=self._open_excel_file, accelerator="Ctrl+O")
        file_menu.add_command(label="保存配置", command=self._save_config, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="导入配置...", command=self._import_config)
        file_menu.add_command(label="导出配置...", command=self._export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing, accelerator="Ctrl+Q")
        
        # 数据库菜单
        db_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="数据库", menu=db_menu)
        db_menu.add_command(label="数据库配置...", command=self._open_db_config)
        db_menu.add_command(label="测试连接", command=self._test_connection)
        db_menu.add_separator()
        db_menu.add_command(label="SQL脚本管理...", command=self._open_sql_manager)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="应用设置...", command=self._open_settings)
        tools_menu.add_command(label="备份配置", command=self._backup_config)
        tools_menu.add_separator()
        tools_menu.add_command(label="清理日志", command=self._cleanup_logs)
        tools_menu.add_command(label="清理备份", command=self._cleanup_backups)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="用户手册", command=self._show_manual)
        help_menu.add_command(label="快捷键", command=self._show_shortcuts)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self._show_about)
        
        # 绑定快捷键
        self.root.bind('<Control-o>', lambda e: self._open_excel_file())
        self.root.bind('<Control-s>', lambda e: self._save_config())
        self.root.bind('<Control-q>', lambda e: self._on_closing())
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
        
        # 文件操作按钮
        ttk.Button(toolbar, text="打开Excel", command=self._open_excel_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="保存配置", command=self._save_config).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 数据库操作按钮
        ttk.Button(toolbar, text="数据库配置", command=self._open_db_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="测试连接", command=self._test_connection).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # SQL脚本按钮
        ttk.Button(toolbar, text="SQL管理", command=self._open_sql_manager).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="执行查询", command=self._execute_query).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 数据处理按钮
        ttk.Button(toolbar, text="填充数据", command=self._fill_excel_data, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=2)
        
        # 右侧状态信息
        status_frame = ttk.Frame(toolbar)
        status_frame.pack(side=tk.RIGHT, padx=10)
        
        ttk.Label(status_frame, text="数据库:").pack(side=tk.LEFT)
        self.db_status_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.db_status_label.pack(side=tk.LEFT, padx=(5, 0))
    
    def _create_main_area(self):
        """创建主工作区"""
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Excel文件管理选项卡
        self.excel_manager = ExcelFileManager(self.notebook, self.config_manager, self.db_manager)
        self.notebook.add(self.excel_manager.frame, text="Excel文件处理")
        
        # SQL脚本管理选项卡
        self.sql_manager = SqlScriptManager(self.notebook, self.config_manager, self.db_manager)
        self.notebook.add(self.sql_manager.frame, text="SQL脚本管理")
        
        # 数据库配置选项卡
        self.db_config_frame = self._create_db_config_tab()
        self.notebook.add(self.db_config_frame, text="数据库配置")
        
        # 日志查看选项卡
        self.log_frame = self._create_log_tab()
        self.notebook.add(self.log_frame, text="日志查看")
    
    def _create_db_config_tab(self):
        """创建数据库配置选项卡"""
        frame = ttk.Frame(self.notebook)
        
        # 配置列表
        list_frame = ttk.LabelFrame(frame, text="数据库配置列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ("名称", "类型", "主机", "数据库", "状态")
        self.config_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.config_tree.heading(col, text=col)
            self.config_tree.column(col, width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.config_tree.yview)
        self.config_tree.configure(yscrollcommand=scrollbar.set)
        
        self.config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(btn_frame, text="新建配置", command=self._new_db_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑配置", command=self._edit_db_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除配置", command=self._delete_db_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="设为当前", command=self._set_current_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新列表", command=self._refresh_config_list).pack(side=tk.LEFT, padx=5)
        
        return frame
    
    def _create_log_tab(self):
        """创建日志查看选项卡"""
        frame = ttk.Frame(self.notebook)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(frame, text="应用日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制按钮
        log_btn_frame = ttk.Frame(frame)
        log_btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(log_btn_frame, text="刷新日志", command=self._refresh_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_btn_frame, text="清空显示", command=self._clear_log_display).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_btn_frame, text="保存日志", command=self._save_logs).pack(side=tk.LEFT, padx=5)
        
        # 日志级别选择
        ttk.Label(log_btn_frame, text="日志级别:").pack(side=tk.LEFT, padx=(20, 5))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(log_btn_frame, textvariable=self.log_level_var, 
                                      values=["DEBUG", "INFO", "WARNING", "ERROR"], 
                                      state="readonly", width=10)
        log_level_combo.pack(side=tk.LEFT, padx=5)
        log_level_combo.bind("<<ComboboxSelected>>", self._on_log_level_changed)
        
        return frame
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.status_bar, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_bar, variable=self.progress_var, 
                                          length=200, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 时间显示
        self.time_var = tk.StringVar()
        time_label = ttk.Label(self.status_bar, textvariable=self.time_var)
        time_label.pack(side=tk.RIGHT, padx=10)
        
        # 定时更新时间
        self._update_time()
    
    def _update_time(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self._update_time)
    
    def _load_settings(self):
        """加载应用设置"""
        try:
            settings = self.config_manager.load_app_settings()
            
            # 应用窗口几何设置
            if 'window_geometry' in settings:
                self.root.geometry(settings['window_geometry'])
            
            # 刷新配置列表
            self._refresh_config_list()
            
            logger.info("应用设置加载完成")
        except Exception as e:
            logger.error(f"加载应用设置失败: {e}")
    
    def _refresh_config_list(self):
        """刷新数据库配置列表"""
        try:
            # 清空现有项目
            for item in self.config_tree.get_children():
                self.config_tree.delete(item)
            
            # 加载配置
            configs = self.config_manager.load_database_configs()
            current_config = self.config_manager.get_current_database_config()
            
            for config in configs:
                status = "当前" if config.name == current_config else "可用"
                self.config_tree.insert("", tk.END, values=(
                    config.name,
                    config.db_type.upper(),
                    f"{config.host}:{config.port}",
                    config.database,
                    status
                ))
            
            # 更新数据库状态显示
            if current_config:
                self.db_status_label.config(text=current_config, foreground="green")
            else:
                self.db_status_label.config(text="未连接", foreground="red")
                
        except Exception as e:
            logger.error(f"刷新配置列表失败: {e}")
            messagebox.showerror("错误", f"刷新配置列表失败: {e}")
    
    # 菜单和按钮事件处理方法
    def _open_excel_file(self):
        """打开Excel文件"""
        if self.excel_manager:
            self.excel_manager.open_file()
    
    def _save_config(self):
        """保存配置"""
        try:
            # 这里可以保存当前的所有配置
            self.update_status("配置已保存")
            logger.info("配置保存完成")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _open_db_config(self):
        """打开数据库配置对话框"""
        if not self.db_config_dialog:
            self.db_config_dialog = DatabaseConfigDialog(self.root, self.config_manager)
        
        result = self.db_config_dialog.show()
        if result:
            self._refresh_config_list()
    
    def _test_connection(self):
        """测试数据库连接"""
        try:
            current_config = self.config_manager.get_current_database_config()
            if not current_config:
                messagebox.showwarning("警告", "请先选择一个数据库配置")
                return
            
            # 这里添加连接测试逻辑
            self.update_status("正在测试连接...")
            # TODO: 实现连接测试
            messagebox.showinfo("成功", "数据库连接测试成功")
            self.update_status("连接测试成功")
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            messagebox.showerror("错误", f"连接测试失败: {e}")
            self.update_status("连接测试失败")
    
    def _open_sql_manager(self):
        """打开SQL脚本管理器"""
        if self.sql_manager:
            self.notebook.select(1)  # 切换到SQL管理选项卡
    
    def _execute_query(self):
        """执行查询"""
        if self.sql_manager:
            self.sql_manager.execute_current_script()
    
    def _fill_excel_data(self):
        """填充Excel数据"""
        if self.excel_manager:
            self.excel_manager.fill_data()
    
    def _open_settings(self):
        """打开设置对话框"""
        dialog = SettingsDialog(self.root, self.config_manager)
        if dialog.show():
            self._load_settings()
    
    def _backup_config(self):
        """备份配置"""
        try:
            result = self.config_manager.backup_configs()
            messagebox.showinfo("成功", result)
            self.update_status("配置备份完成")
        except Exception as e:
            logger.error(f"备份配置失败: {e}")
            messagebox.showerror("错误", f"备份配置失败: {e}")
    
    def _show_about(self):
        """显示关于对话框"""
        AboutDialog(self.root).show()
    
    def update_status(self, message: str):
        """更新状态栏消息"""
        self.status_var.set(message)
        logger.info(f"状态更新: {message}")
    
    def update_progress(self, value: float):
        """更新进度条"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            # 保存窗口几何信息
            settings = self.config_manager.load_app_settings()
            settings['window_geometry'] = self.root.geometry()
            self.config_manager.save_app_settings(settings)
            
            # 关闭数据库连接
            if self.db_manager:
                # TODO: 添加关闭连接的方法
                pass
            
            logger.info("应用程序正常退出")
            self.root.destroy()
            
        except Exception as e:
            logger.error(f"退出时发生错误: {e}")
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"主窗口运行错误: {e}")
            raise AppException(f"主窗口运行错误: {e}")