"""
目录管理工具
"""

import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        "config",
        "scripts", 
        "templates",
        "output",
        "logs",
        "backup",
        "history",
        "assets",
        "assets/icons",
        "docs",
        "tests"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.debug(f"目录确认存在: {directory}")
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            raise
    
    logger.info("所有必要目录已确认存在")


def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent


def get_config_dir() -> Path:
    """获取配置目录"""
    return get_project_root() / "config"


def get_output_dir() -> Path:
    """获取输出目录"""
    return get_project_root() / "output"


def get_templates_dir() -> Path:
    """获取模板目录"""
    return get_project_root() / "templates"


def get_logs_dir() -> Path:
    """获取日志目录"""
    return get_project_root() / "logs"


def get_backup_dir() -> Path:
    """获取备份目录"""
    return get_project_root() / "backup"