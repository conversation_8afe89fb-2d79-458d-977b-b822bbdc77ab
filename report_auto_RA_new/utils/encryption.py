"""
加密工具模块
"""

import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
from pathlib import Path


class PasswordEncryption:
    """密码加密工具类"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self):
        """获取或创建加密密钥"""
        key_file = Path("config") / ".key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # 生成新密钥
            password = b"report_auto_ra_default_key_2024"
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            
            # 保存密钥
            key_file.parent.mkdir(exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            
            return key
    
    def encrypt(self, password: str) -> str:
        """加密密码"""
        if not password:
            return ""
        return self.cipher.encrypt(password.encode()).decode()
    
    def decrypt(self, encrypted_password: str) -> str:
        """解密密码"""
        if not encrypted_password:
            return ""
        return self.cipher.decrypt(encrypted_password.encode()).decode()


# 全局加密实例
_encryption = PasswordEncryption()


def encrypt_password(password: str) -> str:
    """加密密码"""
    return _encryption.encrypt(password)


def decrypt_password(encrypted_password: str) -> str:
    """解密密码"""
    return _encryption.decrypt(encrypted_password)