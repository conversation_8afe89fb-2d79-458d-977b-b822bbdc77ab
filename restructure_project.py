#!/usr/bin/env python3
"""
项目结构重组脚本
将现有项目重组为新的目录结构
"""

import os
import shutil
from pathlib import Path

def create_directory_structure():
    """创建新的目录结构"""
    
    # 定义新的目录结构
    directories = [
        # 应用程序核心
        "app",
        
        # 核心业务逻辑
        "core",
        "core/database",
        "core/database/adapters",
        "core/excel", 
        "core/scripts",
        "core/reports",
        
        # 用户界面
        "ui",
        "ui/dialogs",
        "ui/widgets", 
        "ui/resources",
        "ui/resources/icons",
        "ui/resources/styles",
        "ui/resources/images",
        
        # 配置管理
        "config",
        "config/schemas",
        
        # 工具模块
        "utils",
        
        # 异常定义
        "exceptions",
        
        # 数据目录
        "data",
        "data/config",
        "data/templates",
        "data/templates/custom",
        "data/scripts",
        "data/scripts/sql",
        "data/output",
        "data/backup", 
        "data/cache",
        
        # 日志目录
        "logs",
        
        # 测试代码
        "tests",
        "tests/unit",
        "tests/unit/core",
        "tests/unit/ui",
        "tests/unit/config",
        "tests/unit/utils",
        "tests/integration",
        "tests/fixtures",
        "tests/fixtures/mock_data",
        "tests/reports",
        
        # 文档目录
        "docs",
        "docs/images",
        
        # 构建脚本
        "scripts",
        
        # 分发目录
        "dist",
        "dist/installer"
    ]
    
    # 创建目录
    base_path = Path("report_auto_RA_new")
    base_path.mkdir(exist_ok=True)
    
    for directory in directories:
        dir_path = base_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建__init__.py文件（对于Python包）
        if not directory.startswith(("data", "logs", "docs", "scripts", "dist", "ui/resources")):
            init_file = dir_path / "__init__.py"
            if not init_file.exists():
                init_file.write_text('"""{}模块"""\n'.format(directory.replace("/", ".")))
    
    print(f"✅ 目录结构创建完成: {base_path}")
    return base_path

def create_core_files(base_path):
    """创建核心文件"""
    
    # 创建main.py
    main_content = '''#!/usr/bin/env python3
"""
Report Auto RA - 主程序入口
Excel数据库查询填充工具
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.app import ReportAutoApp

def main():
    """主函数"""
    app = ReportAutoApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
'''
    
    (base_path / "main.py").write_text(main_content, encoding='utf-8')
    
    # 创建app.py
    app_content = '''"""
应用程序主类
"""

import logging
from pathlib import Path
from config.manager import ConfigManager
from utils.logger import setup_logger

class ReportAutoApp:
    """Report Auto RA 应用程序主类"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = setup_logger()
        self.logger.info("应用程序初始化")
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动 Report Auto RA")
            
            # 初始化UI
            from ui.main_window import MainWindow
            
            # 创建并显示主窗口
            main_window = MainWindow()
            return main_window.run()
            
        except Exception as e:
            self.logger.error(f"应用程序启动失败: {e}")
            return 1
'''
    
    (base_path / "app" / "app.py").write_text(app_content, encoding='utf-8')
    
    # 创建constants.py
    constants_content = '''"""
全局常量定义
"""

# 应用程序信息
APP_NAME = "Report Auto RA"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Excel数据库查询填充工具"

# 支持的数据库类型
SUPPORTED_DATABASES = ["mysql", "oracle"]

# 文件扩展名
EXCEL_EXTENSIONS = [".xlsx", ".xls"]
SQL_EXTENSIONS = [".sql"]

# 默认配置
DEFAULT_CONFIG = {
    "database": {
        "timeout": 30,
        "max_connections": 5
    },
    "excel": {
        "max_rows": 10000,
        "date_format": "%Y-%m-%d"
    },
    "ui": {
        "theme": "default",
        "language": "zh_CN"
    }
}
'''
    
    (base_path / "app" / "constants.py").write_text(constants_content, encoding='utf-8')
    
    # 创建version.py
    version_content = '''"""
版本信息
"""

__version__ = "1.0.0"
__author__ = "Report Auto RA Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

VERSION_INFO = {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "pre_release": None
}

def get_version():
    """获取版本字符串"""
    version = f"{VERSION_INFO['major']}.{VERSION_INFO['minor']}.{VERSION_INFO['patch']}"
    if VERSION_INFO['pre_release']:
        version += f"-{VERSION_INFO['pre_release']}"
    return version
'''
    
    (base_path / "app" / "version.py").write_text(version_content, encoding='utf-8')
    
    print("✅ 核心文件创建完成")

def create_config_files(base_path):
    """创建配置文件"""
    
    # 创建.env.example
    env_example = '''# 环境变量配置示例
# 复制此文件为.env并填入实际值

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database

# 应用配置
APP_DEBUG=false
APP_LOG_LEVEL=INFO

# 加密密钥
ENCRYPTION_KEY=your_encryption_key_here
'''
    
    (base_path / ".env.example").write_text(env_example, encoding='utf-8')
    
    # 创建.gitignore
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 环境变量
.env
.venv
env/
venv/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 项目特定
data/config/*.json
data/output/*
data/backup/*
data/cache/*
logs/*.log
htmlcov/
.coverage
.pytest_cache/

# 系统文件
.DS_Store
Thumbs.db
'''
    
    (base_path / ".gitignore").write_text(gitignore_content, encoding='utf-8')
    
    print("✅ 配置文件创建完成")

def main():
    """主函数"""
    print("🚀 开始项目结构重组...")
    
    # 创建目录结构
    base_path = create_directory_structure()
    
    # 创建核心文件
    create_core_files(base_path)
    
    # 创建配置文件
    create_config_files(base_path)
    
    print("\n" + "="*60)
    print("🎉 项目结构重组完成！")
    print(f"📁 新项目位置: {base_path}")
    print("\n📋 下一步:")
    print("1. 迁移现有代码到新结构")
    print("2. 更新导入路径")
    print("3. 运行测试验证")
    print("="*60)

if __name__ == "__main__":
    main()
