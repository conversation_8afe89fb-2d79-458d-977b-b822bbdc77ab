#!/usr/bin/env python3
"""
简化的测试运行脚本
"""

import sys
import pytest
from pathlib import Path
from datetime import datetime

def setup_environment():
    """设置测试环境"""
    test_dir = Path(__file__).parent
    project_root = test_dir.parent

    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))

    return test_dir, project_root

def run_basic_tests(test_dir):
    """运行基础测试"""
    print("🧪 运行基础测试套件...")
    print("=" * 60)

    test_files = [
        "test_database_config.py",
        "test_encryption.py",
        "test_database_manager.py"
    ]

    passed_tests = 0
    total_tests = len(test_files)

    for test_file in test_files:
        test_path = test_dir / test_file
        if test_path.exists():
            print(f"\n📋 运行测试: {test_file}")
            result = pytest.main(["-v", str(test_path)])
            if result == 0:
                print(f"✅ 测试通过: {test_file}")
                passed_tests += 1
            else:
                print(f"❌ 测试失败: {test_file}")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")

    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
    return passed_tests == total_tests

def main():
    """主函数"""
    # 设置环境
    test_dir, project_root = setup_environment()

    print("🚀 Report Auto RA 测试套件")
    print("=" * 60)
    print(f"项目路径: {project_root}")
    print(f"测试路径: {test_dir}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    success = run_basic_tests(test_dir)

    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试执行完成！")
    else:
        print("❌ 部分测试失败，请检查上述输出")

    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
