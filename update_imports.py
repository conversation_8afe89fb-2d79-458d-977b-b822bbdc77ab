#!/usr/bin/env python3
"""
更新导入路径脚本
将迁移后的代码中的导入路径更新为新的结构
"""

import re
from pathlib import Path

def update_database_imports():
    """更新数据库模块的导入路径"""
    base_path = Path("report_auto_RA_new")
    
    # 更新数据库管理器
    manager_file = base_path / "core/database/manager.py"
    if manager_file.exists():
        content = manager_file.read_text(encoding='utf-8')
        
        # 更新导入路径
        content = re.sub(
            r'from \.adapters\.mysql_adapter import MySQLAdapter',
            'from .adapters.mysql import MySQLAdapter',
            content
        )
        content = re.sub(
            r'from \.adapters\.oracle_adapter import OracleAdapter', 
            'from .adapters.oracle import OracleAdapter',
            content
        )
        content = re.sub(
            r'from \.config import DatabaseConfig',
            'from .models import DatabaseConfig',
            content
        )
        content = re.sub(
            r'from \.\.exceptions\.app_exceptions import DatabaseException',
            'from ...exceptions.database import DatabaseException',
            content
        )
        
        manager_file.write_text(content, encoding='utf-8')
        print("✅ 更新数据库管理器导入路径")
    
    # 更新适配器文件
    adapters_dir = base_path / "core/database/adapters"
    
    # 更新MySQL适配器
    mysql_file = adapters_dir / "mysql.py"
    if mysql_file.exists():
        content = mysql_file.read_text(encoding='utf-8')
        content = re.sub(
            r'from \.base_adapter import BaseAdapter',
            'from .base import BaseAdapter',
            content
        )
        content = re.sub(
            r'from \.\.\.utils\.encryption import decrypt_password',
            'from ....utils.encryption import decrypt_password',
            content
        )
        content = re.sub(
            r'from \.\.\.exceptions\.app_exceptions import DatabaseException',
            'from ....exceptions.database import DatabaseException',
            content
        )
        
        mysql_file.write_text(content, encoding='utf-8')
        print("✅ 更新MySQL适配器导入路径")
    
    # 更新Oracle适配器
    oracle_file = adapters_dir / "oracle.py"
    if oracle_file.exists():
        content = oracle_file.read_text(encoding='utf-8')
        content = re.sub(
            r'from \.base_adapter import BaseAdapter',
            'from .base import BaseAdapter',
            content
        )
        content = re.sub(
            r'from \.\.\.utils\.encryption import decrypt_password',
            'from ....utils.encryption import decrypt_password',
            content
        )
        content = re.sub(
            r'from \.\.\.exceptions\.app_exceptions import DatabaseException',
            'from ....exceptions.database import DatabaseException',
            content
        )
        
        oracle_file.write_text(content, encoding='utf-8')
        print("✅ 更新Oracle适配器导入路径")

def update_ui_imports():
    """更新UI模块的导入路径"""
    base_path = Path("report_auto_RA_new")
    
    # 更新主窗口
    main_window_file = base_path / "ui/main_window.py"
    if main_window_file.exists():
        content = main_window_file.read_text(encoding='utf-8')
        
        # 更新导入路径
        content = re.sub(
            r'from \.\.database\.manager import DatabaseManager',
            'from ..core.database.manager import DatabaseManager',
            content
        )
        content = re.sub(
            r'from \.\.config\.config_manager import ConfigManager',
            'from ..config.manager import ConfigManager',
            content
        )
        content = re.sub(
            r'from \.database_config_dialog import DatabaseConfigDialog',
            'from .dialogs.database_config import DatabaseConfigDialog',
            content
        )
        content = re.sub(
            r'from \.excel_file_manager import ExcelFileManager',
            'from .dialogs.excel_manager import ExcelFileManager',
            content
        )
        content = re.sub(
            r'from \.sql_script_manager import SQLScriptManager',
            'from .dialogs.script_manager import SQLScriptManager',
            content
        )
        content = re.sub(
            r'from \.settings_dialog import SettingsDialog',
            'from .dialogs.settings import SettingsDialog',
            content
        )
        content = re.sub(
            r'from \.about_dialog import AboutDialog',
            'from .dialogs.about import AboutDialog',
            content
        )
        
        main_window_file.write_text(content, encoding='utf-8')
        print("✅ 更新主窗口导入路径")
    
    # 更新对话框文件
    dialogs_dir = base_path / "ui/dialogs"
    for dialog_file in dialogs_dir.glob("*.py"):
        if dialog_file.name == "__init__.py":
            continue
            
        content = dialog_file.read_text(encoding='utf-8')
        
        # 通用导入路径更新
        content = re.sub(
            r'from \.\.\.database\.config import DatabaseConfig',
            'from ...core.database.models import DatabaseConfig',
            content
        )
        content = re.sub(
            r'from \.\.\.database\.manager import DatabaseManager',
            'from ...core.database.manager import DatabaseManager',
            content
        )
        content = re.sub(
            r'from \.\.\.config\.config_manager import ConfigManager',
            'from ...config.manager import ConfigManager',
            content
        )
        content = re.sub(
            r'from \.\.\.utils\.',
            'from ...utils.',
            content
        )
        
        dialog_file.write_text(content, encoding='utf-8')
        print(f"✅ 更新对话框 {dialog_file.name} 导入路径")

def update_test_imports():
    """更新测试文件的导入路径"""
    base_path = Path("report_auto_RA_new")
    
    # 更新数据库模型测试
    test_file = base_path / "tests/unit/core/test_database_models.py"
    if test_file.exists():
        content = test_file.read_text(encoding='utf-8')
        
        content = re.sub(
            r'from src\.database\.config import DatabaseConfig',
            'from core.database.models import DatabaseConfig',
            content
        )
        
        test_file.write_text(content, encoding='utf-8')
        print("✅ 更新数据库模型测试导入路径")
    
    # 更新数据库管理器测试
    test_file = base_path / "tests/unit/core/test_database_manager.py"
    if test_file.exists():
        content = test_file.read_text(encoding='utf-8')
        
        content = re.sub(
            r'from src\.database\.manager import DatabaseManager',
            'from core.database.manager import DatabaseManager',
            content
        )
        content = re.sub(
            r'from src\.database\.config import DatabaseConfig',
            'from core.database.models import DatabaseConfig',
            content
        )
        
        test_file.write_text(content, encoding='utf-8')
        print("✅ 更新数据库管理器测试导入路径")
    
    # 更新加密测试
    test_file = base_path / "tests/unit/utils/test_encryption.py"
    if test_file.exists():
        content = test_file.read_text(encoding='utf-8')
        
        content = re.sub(
            r'from src\.utils\.encryption import',
            'from utils.encryption import',
            content
        )
        
        test_file.write_text(content, encoding='utf-8')
        print("✅ 更新加密测试导入路径")

def update_config_imports():
    """更新配置模块的导入路径"""
    base_path = Path("report_auto_RA_new")
    
    # 更新配置管理器
    config_file = base_path / "config/manager.py"
    if config_file.exists():
        content = config_file.read_text(encoding='utf-8')
        
        content = re.sub(
            r'from \.\.utils\.',
            'from ..utils.',
            content
        )
        content = re.sub(
            r'from \.\.exceptions\.app_exceptions',
            'from ..exceptions.config',
            content
        )
        
        config_file.write_text(content, encoding='utf-8')
        print("✅ 更新配置管理器导入路径")

def main():
    """主函数"""
    print("🚀 开始更新导入路径...")
    print("="*60)
    
    if not Path("report_auto_RA_new").exists():
        print("❌ 新项目目录不存在")
        return
    
    update_database_imports()
    print()
    
    update_ui_imports()
    print()
    
    update_test_imports()
    print()
    
    update_config_imports()
    
    print("\n" + "="*60)
    print("🎉 导入路径更新完成！")
    print("\n📋 下一步:")
    print("1. 创建缺失的模块")
    print("2. 运行测试验证")
    print("3. 修复剩余的导入问题")
    print("="*60)

if __name__ == "__main__":
    main()
